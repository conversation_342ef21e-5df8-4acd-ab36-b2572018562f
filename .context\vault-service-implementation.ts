// ChatLo Vault Service Implementation
// This file contains the proposed TypeScript implementation for the vault system

import Database from 'better-sqlite3'
import * as fs from 'fs'
import * as path from 'path'
import { app } from 'electron'
import { v4 as uuidv4 } from 'uuid'

// Interfaces
export interface VaultConfig {
  id: string
  name: string
  path: string
  description?: string
  color?: string
  icon?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ContextConfig {
  id: string
  vaultId: string
  name: string
  folderPath: string
  masterDocPath?: string
  description?: string
  tags?: string[]
  color?: string
  icon?: string
  isPinned: boolean
  fileCount: number
  lastActivity?: string
  createdAt: string
  updatedAt: string
}

export interface SearchOptions {
  vaultIds?: string[]
  contextIds?: string[]
  fileTypes?: string[]
  includeContent?: boolean
  deepSearch?: boolean
  limit?: number
}

export interface SearchResult {
  fileId: string
  vaultId: string
  contextId?: string
  filename: string
  filepath: string
  snippet: string
  matchType: 'filename' | 'content' | 'deep'
  relevanceScore: number
  contextName?: string
}

export interface ContextTemplate {
  name: string
  description?: string
  tags?: string[]
  color?: string
  icon?: string
  folderStructure?: string[]
  masterTemplate?: string
}

// Global Search Service
export class GlobalSearchService {
  private systemDb: Database.Database
  private searchIndex: Map<string, VaultSearcher> = new Map()

  constructor() {
    // Global search database
    const systemDbPath = path.join(app.getPath('userData'), 'chatlo-search.db')
    this.systemDb = new Database(systemDbPath)
    this.initializeSearchTables()
  }

  private initializeSearchTables(): void {
    this.systemDb.exec(`
      -- Global file index (metadata only, no content)
      CREATE TABLE IF NOT EXISTS global_file_index (
        id TEXT PRIMARY KEY,
        vault_id TEXT NOT NULL,
        context_id TEXT,
        filename TEXT NOT NULL,
        filepath TEXT NOT NULL,
        relative_path TEXT NOT NULL,
        file_type TEXT NOT NULL,
        file_size INTEGER NOT NULL,
        content_hash TEXT NOT NULL,
        mime_type TEXT,
        is_processed INTEGER DEFAULT 0,
        processing_status TEXT DEFAULT 'pending',
        last_modified TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Full-text search index (content excerpts only)
      CREATE VIRTUAL TABLE IF NOT EXISTS global_content_fts USING fts5(
        file_id,
        vault_id,
        context_id,
        filename,
        content_excerpt,
        tokenize = 'porter'
      );

      -- Context index
      CREATE TABLE IF NOT EXISTS global_context_index (
        id TEXT PRIMARY KEY,
        vault_id TEXT NOT NULL,
        name TEXT NOT NULL,
        folder_path TEXT NOT NULL,
        description TEXT,
        file_count INTEGER DEFAULT 0,
        last_activity TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );

      -- Performance indexes
      CREATE INDEX IF NOT EXISTS idx_global_files_vault ON global_file_index (vault_id);
      CREATE INDEX IF NOT EXISTS idx_global_files_context ON global_file_index (context_id);
      CREATE INDEX IF NOT EXISTS idx_global_files_type ON global_file_index (file_type);
      CREATE INDEX IF NOT EXISTS idx_global_files_hash ON global_file_index (content_hash);
      CREATE INDEX IF NOT EXISTS idx_global_context_vault ON global_context_index (vault_id);
    `)
  }

  // Global search across all vaults
  async searchGlobally(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
    const results: SearchResult[] = []

    // 1. Search file names (fast)
    const fileResults = this.searchFileNames(query, options)
    results.push(...fileResults)

    // 2. Search content excerpts (medium speed)
    if (options.includeContent !== false) {
      const contentResults = await this.searchContentExcerpts(query, options)
      results.push(...contentResults)
    }

    // 3. Search full content in specific vaults (slower, on-demand)
    if (options.deepSearch && options.vaultIds) {
      const deepResults = await this.searchFullContent(query, options.vaultIds)
      results.push(...deepResults)
    }

    return this.deduplicateAndRank(results)
  }

  private searchFileNames(query: string, options: SearchOptions): SearchResult[] {
    const stmt = this.systemDb.prepare(`
      SELECT 
        gfi.*,
        gci.name as context_name,
        'filename' as match_type,
        1 as relevance_score
      FROM global_file_index gfi
      LEFT JOIN global_context_index gci ON gfi.context_id = gci.id
      WHERE gfi.filename LIKE ? ESCAPE '\\'
      ${options.vaultIds ? 'AND gfi.vault_id IN (' + options.vaultIds.map(() => '?').join(',') + ')' : ''}
      ${options.fileTypes ? 'AND gfi.file_type IN (' + options.fileTypes.map(() => '?').join(',') + ')' : ''}
      ORDER BY gfi.last_modified DESC
      LIMIT ${options.limit || 20}
    `)

    const term = `%${query.replace(/%/g, '\\%').replace(/_/g, '\\_')}%`
    const params = [term, ...(options.vaultIds || []), ...(options.fileTypes || [])]
    
    return stmt.all(...params).map(row => ({
      ...row,
      snippet: `Found in filename: ${row.filename}`
    })) as SearchResult[]
  }

  private async searchContentExcerpts(query: string, options: SearchOptions): Promise<SearchResult[]> {
    const stmt = this.systemDb.prepare(`
      SELECT 
        gcf.file_id,
        gcf.vault_id,
        gcf.context_id,
        gcf.filename,
        snippet(global_content_fts, 4, '<mark>', '</mark>', '...', 32) as snippet,
        rank as relevance_score,
        'content' as match_type
      FROM global_content_fts gcf
      WHERE global_content_fts MATCH ?
      ${options.vaultIds ? 'AND gcf.vault_id IN (' + options.vaultIds.map(() => '?').join(',') + ')' : ''}
      ORDER BY rank
      LIMIT ${options.limit || 20}
    `)

    const params = [query, ...(options.vaultIds || [])]
    return stmt.all(...params) as SearchResult[]
  }

  private async searchFullContent(query: string, vaultIds: string[]): Promise<SearchResult[]> {
    // This would search actual file content in specified vaults
    // Implementation depends on vault-specific search services
    const results: SearchResult[] = []
    
    for (const vaultId of vaultIds) {
      const vaultSearcher = this.searchIndex.get(vaultId)
      if (vaultSearcher) {
        const vaultResults = await vaultSearcher.searchFullContent(query)
        results.push(...vaultResults)
      }
    }
    
    return results
  }

  private deduplicateAndRank(results: SearchResult[]): SearchResult[] {
    // Remove duplicates and sort by relevance
    const seen = new Set<string>()
    const unique = results.filter(result => {
      const key = `${result.fileId}-${result.matchType}`
      if (seen.has(key)) return false
      seen.add(key)
      return true
    })

    return unique.sort((a, b) => b.relevanceScore - a.relevanceScore)
  }

  // Index file content (called when files are processed)
  async indexFileContent(fileId: string, vaultId: string, contextId: string | null, 
                        filename: string, content: string): Promise<void> {
    // Store only excerpt in global index for performance
    const excerpt = content.substring(0, 1000) // First 1000 chars
    
    const stmt = this.systemDb.prepare(`
      INSERT OR REPLACE INTO global_content_fts 
      (file_id, vault_id, context_id, filename, content_excerpt)
      VALUES (?, ?, ?, ?, ?)
    `)
    
    stmt.run(fileId, vaultId, contextId, filename, excerpt)
  }

  // Update file index when files change
  async updateFileIndex(fileRecord: any, vaultId: string, contextId?: string): Promise<void> {
    const stmt = this.systemDb.prepare(`
      INSERT OR REPLACE INTO global_file_index 
      (id, vault_id, context_id, filename, filepath, relative_path, file_type, file_size, 
       content_hash, mime_type, is_processed, processing_status, last_modified)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `)
    
    stmt.run(
      fileRecord.id,
      vaultId,
      contextId || null,
      fileRecord.filename,
      fileRecord.filepath,
      fileRecord.relative_path || fileRecord.filename,
      fileRecord.file_type,
      fileRecord.file_size,
      fileRecord.content_hash,
      fileRecord.mime_type,
      fileRecord.extracted_content ? 1 : 0,
      fileRecord.extracted_content ? 'completed' : 'pending',
      fileRecord.updated_at
    )
  }

  // Clean up index when files are deleted
  async removeFromIndex(fileId: string): Promise<void> {
    const fileStmt = this.systemDb.prepare('DELETE FROM global_file_index WHERE id = ?')
    const contentStmt = this.systemDb.prepare('DELETE FROM global_content_fts WHERE file_id = ?')
    
    fileStmt.run(fileId)
    contentStmt.run(fileId)
  }
}

// Vault-specific searcher
class VaultSearcher {
  private vaultPath: string
  private vaultDb: Database.Database

  constructor(vaultPath: string) {
    this.vaultPath = vaultPath
    this.vaultDb = new Database(path.join(vaultPath, '.vault', 'index.db'))
    this.initializeVaultDb()
  }

  private initializeVaultDb(): void {
    this.vaultDb.exec(`
      -- Context-specific relationships
      CREATE TABLE IF NOT EXISTS context_files (
        context_id TEXT NOT NULL,
        file_path TEXT NOT NULL,
        relationship_type TEXT DEFAULT 'contains',
        added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (context_id, file_path)
      );

      -- Chat-context links
      CREATE TABLE IF NOT EXISTS vault_chat_context_links (
        chat_id TEXT NOT NULL,
        context_id TEXT NOT NULL,
        linked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (chat_id, context_id)
      );
    `)
  }

  async searchFullContent(query: string): Promise<SearchResult[]> {
    // Implementation for deep content search within vault
    // This would read actual files and search their full content
    const results: SearchResult[] = []
    
    // Get all contexts in this vault
    const contexts = await this.getAllContexts()
    
    for (const context of contexts) {
      const contextResults = await this.searchInContext(context.id, query)
      results.push(...contextResults)
    }
    
    return results
  }

  private async getAllContexts(): Promise<ContextConfig[]> {
    // Implementation to get all contexts in this vault
    return []
  }

  private async searchInContext(contextId: string, query: string): Promise<SearchResult[]> {
    // Implementation to search within a specific context
    return []
  }
}

// Main Vault Service
export class VaultService {
  private vaultRootPath: string
  private activeVaultId: string | null = null
  private globalSearch: GlobalSearchService
  
  constructor(private dbManager: any) {
    this.vaultRootPath = path.join(app.getPath('documents'), 'ChatLo-Vaults')
    this.globalSearch = new GlobalSearchService()
  }

  // Vault Operations
  async initializeVaultSystem(): Promise<void> {
    // Create vault root if not exists
    if (!fs.existsSync(this.vaultRootPath)) {
      fs.mkdirSync(this.vaultRootPath, { recursive: true })
      await this.createSystemMetadata()
    }
    
    // Scan for existing vaults
    await this.scanForVaults()
  }

  private async createSystemMetadata(): Promise<void> {
    const systemDir = path.join(this.vaultRootPath, '.chatlo')
    fs.mkdirSync(systemDir, { recursive: true })
    
    // Create vault registry
    const registryPath = path.join(systemDir, 'vault-registry.json')
    fs.writeFileSync(registryPath, JSON.stringify({ vaults: [] }, null, 2))
    
    // Create preferences
    const preferencesPath = path.join(systemDir, 'preferences.json')
    fs.writeFileSync(preferencesPath, JSON.stringify({ 
      activeVaultId: null,
      searchSettings: {
        includeContent: true,
        maxResults: 50
      }
    }, null, 2))
  }

  async createVault(name: string, description?: string): Promise<string> {
    const vaultId = uuidv4()
    const vaultPath = path.join(this.vaultRootPath, this.sanitizeFolderName(name))
    
    // Create vault folder structure
    fs.mkdirSync(vaultPath, { recursive: true })
    fs.mkdirSync(path.join(vaultPath, '.vault'), { recursive: true })
    
    // Create vault metadata
    const vaultConfig: VaultConfig = {
      id: vaultId,
      name,
      path: vaultPath,
      description,
      color: '#8AB0BB',
      icon: 'folder',
      isActive: true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // Save to database
    this.dbManager.addVault(vaultConfig)
    
    // Create vault metadata file
    await this.saveVaultMetadata(vaultPath, vaultConfig)
    
    return vaultId
  }

  private sanitizeFolderName(name: string): string {
    return name.replace(/[<>:"/\\|?*]/g, '-').replace(/\s+/g, '-').toLowerCase()
  }

  private async saveVaultMetadata(vaultPath: string, config: VaultConfig): Promise<void> {
    const metadataPath = path.join(vaultPath, '.vault', 'metadata.json')
    fs.writeFileSync(metadataPath, JSON.stringify(config, null, 2))
  }

  async scanForVaults(): Promise<VaultConfig[]> {
    const vaults: VaultConfig[] = []
    
    if (!fs.existsSync(this.vaultRootPath)) return vaults
    
    const items = fs.readdirSync(this.vaultRootPath)
    
    for (const item of items) {
      if (item.startsWith('.')) continue // Skip system folders
      
      const itemPath = path.join(this.vaultRootPath, item)
      const stats = fs.statSync(itemPath)
      
      if (stats.isDirectory()) {
        const vaultMetadataPath = path.join(itemPath, '.vault', 'metadata.json')
        
        if (fs.existsSync(vaultMetadataPath)) {
          try {
            const metadata = JSON.parse(fs.readFileSync(vaultMetadataPath, 'utf8'))
            vaults.push(metadata)
            
            // Update database if not exists
            const existingVault = this.dbManager.getVaultByPath(itemPath)
            if (!existingVault) {
              this.dbManager.addVault(metadata)
            }
          } catch (error) {
            console.warn('Failed to parse vault metadata:', vaultMetadataPath, error)
          }
        }
      }
    }
    
    return vaults
  }

  // Global search interface
  async searchGlobally(query: string, options?: SearchOptions): Promise<SearchResult[]> {
    return this.globalSearch.searchGlobally(query, options)
  }

  // Get global search service for direct access
  getGlobalSearchService(): GlobalSearchService {
    return this.globalSearch
  }

  // Context Operations
  async createContext(vaultId: string, name: string, template?: ContextTemplate): Promise<string> {
    const vault = this.dbManager.getVault(vaultId)
    if (!vault) throw new Error('Vault not found')

    const contextId = uuidv4()
    const contextPath = path.join(vault.path, this.sanitizeFolderName(name))

    // Create context folder structure
    fs.mkdirSync(contextPath, { recursive: true })
    fs.mkdirSync(path.join(contextPath, '.context'), { recursive: true })
    fs.mkdirSync(path.join(contextPath, '.context', 'insights'), { recursive: true })
    fs.mkdirSync(path.join(contextPath, 'documents'), { recursive: true })
    fs.mkdirSync(path.join(contextPath, 'images'), { recursive: true })
    fs.mkdirSync(path.join(contextPath, 'artifacts'), { recursive: true })

    // Create AI insight files
    const insightsPath = path.join(contextPath, '.context', 'insights')
    fs.writeFileSync(path.join(insightsPath, 'relationships.md'), '# File Relationships\n\n*AI will discover and document file relationships here*\n', 'utf8')
    fs.writeFileSync(path.join(insightsPath, 'patterns.md'), '# Discovered Patterns\n\n*AI will identify and document patterns here*\n', 'utf8')
    fs.writeFileSync(path.join(insightsPath, 'evolution.md'), '# Context Evolution Log\n\n*AI will track context changes and growth here*\n', 'utf8')

    // Create AI memory file
    const aiMemory = {
      contextId,
      createdAt: new Date().toISOString(),
      insights: [],
      patterns: [],
      relationships: [],
      conversationSummaries: []
    }
    fs.writeFileSync(path.join(contextPath, '.context', 'ai-memory.json'), JSON.stringify(aiMemory, null, 2), 'utf8')

    // Create master.md
    const masterContent = this.generateMasterTemplate(name, template)
    fs.writeFileSync(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const contextConfig: ContextConfig = {
      id: contextId,
      vaultId,
      name,
      folderPath: contextPath,
      description: template?.description,
      tags: template?.tags || [],
      color: template?.color || '#8AB0BB',
      icon: template?.icon || 'folder',
      isPinned: false,
      fileCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    // Save to database
    this.dbManager.addContext(contextConfig)

    // Save context metadata
    await this.saveContextMetadata(contextPath, contextConfig)

    return contextId
  }

  private generateMasterTemplate(name: string, template?: ContextTemplate): string {
    return `# ${name}

## 🧠 AI Context Summary
*This section is continuously updated by AI*

**Current Understanding**: New context for ${name}
**Key Relationships**: To be discovered through interactions
**Active Patterns**: Will be identified as files are added
**Last AI Update**: ${new Date().toISOString()}

## 📋 Human Overview
*Human-maintained section*

**Purpose**: ${template?.description || 'Define the purpose of this context'}
**Goals**: What we're trying to achieve with this context
**Status**: Just created, ready for content
**Notes**: Add your insights and observations here

## 🔗 Intelligent Connections

### File Relationships
*AI-discovered and human-curated*
- Files will be automatically analyzed for relationships

### Concept Map
*AI-generated concept relationships*
- Concepts will be mapped as content is added

### Cross-Context Links
*Links to related contexts in other vaults*
- Related contexts will be suggested by AI

## 📚 Knowledge Evolution

### Recent Insights
*AI-generated insights from recent interactions*
- Insights will appear here as AI learns about this context

### Learning Trajectory
*AI tracks how understanding has evolved*
- Context evolution will be tracked automatically

## 🎯 Active Focus Areas
*What AI should pay attention to*

**Current Priorities**:
${template?.tags?.map(tag => `- ${tag}`).join('\n') || '- Define focus areas as context develops'}

**Watch For**:
- New files and their patterns
- Conversations about this context
- Connections to other contexts

## 🤖 AI Instructions
*How AI should behave in this context*

**Personality**: Helpful assistant, learning-oriented
**Communication Style**: Clear, contextual, adaptive
**Focus Areas**: Understanding context purpose and content
**Avoid**: Making assumptions without sufficient information

**Context Loading Priority**:
1. Always read this master.md first
2. Analyze new files for patterns
3. Connect conversations to context understanding
4. Update insights based on interactions

## 📊 Context Metrics
*AI-maintained statistics*

**Files**: 0 documents, 0 images, 0 artifacts
**Conversations**: 0 chats linked to this context
**Activity**: Just created
**Health**: Ready for content
**Growth**: New context, tracking begins now

---
*This master.md is continuously updated by AI to reflect evolving understanding*
*Created: ${new Date().toLocaleDateString()}*
*Last Updated: ${new Date().toLocaleDateString()}*`
  }

  private async saveContextMetadata(contextPath: string, config: ContextConfig): Promise<void> {
    const metadataPath = path.join(contextPath, '.context', 'metadata.json')
    fs.writeFileSync(metadataPath, JSON.stringify(config, null, 2))
  }

  async scanContextsInVault(vaultId: string): Promise<ContextConfig[]> {
    const vault = this.dbManager.getVault(vaultId)
    if (!vault) return []

    const contexts: ContextConfig[] = []
    const items = fs.readdirSync(vault.path)

    for (const item of items) {
      if (item.startsWith('.')) continue // Skip system folders

      const itemPath = path.join(vault.path, item)
      const stats = fs.statSync(itemPath)

      if (stats.isDirectory()) {
        const contextMetadataPath = path.join(itemPath, '.context', 'metadata.json')
        const masterDocPath = path.join(itemPath, 'master.md')

        if (fs.existsSync(contextMetadataPath) || fs.existsSync(masterDocPath)) {
          let contextConfig: ContextConfig

          if (fs.existsSync(contextMetadataPath)) {
            // Load from metadata
            contextConfig = JSON.parse(fs.readFileSync(contextMetadataPath, 'utf8'))
          } else {
            // Create from folder structure
            contextConfig = {
              id: uuidv4(),
              vaultId,
              name: item,
              folderPath: itemPath,
              isPinned: false,
              fileCount: 0,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString()
            }

            // Save metadata for future use
            await this.saveContextMetadata(itemPath, contextConfig)
          }

          contexts.push(contextConfig)

          // Update database
          const existingContext = this.dbManager.getContextByPath(itemPath)
          if (!existingContext) {
            this.dbManager.addContext(contextConfig)
          }
        }
      }
    }

    return contexts
  }

  // Vault management
  async setActiveVault(vaultId: string): Promise<void> {
    this.activeVaultId = vaultId

    // Update preferences
    const preferencesPath = path.join(this.vaultRootPath, '.chatlo', 'preferences.json')
    if (fs.existsSync(preferencesPath)) {
      const preferences = JSON.parse(fs.readFileSync(preferencesPath, 'utf8'))
      preferences.activeVaultId = vaultId
      fs.writeFileSync(preferencesPath, JSON.stringify(preferences, null, 2))
    }
  }

  async getActiveVault(): Promise<VaultConfig | null> {
    if (!this.activeVaultId) {
      // Try to load from preferences
      const preferencesPath = path.join(this.vaultRootPath, '.chatlo', 'preferences.json')
      if (fs.existsSync(preferencesPath)) {
        const preferences = JSON.parse(fs.readFileSync(preferencesPath, 'utf8'))
        this.activeVaultId = preferences.activeVaultId
      }
    }

    return this.activeVaultId ? this.dbManager.getVault(this.activeVaultId) : null
  }

  // Utility methods
  getVaultRootPath(): string {
    return this.vaultRootPath
  }

  async setVaultRootPath(newPath: string): Promise<void> {
    this.vaultRootPath = newPath
    // Re-initialize system
    await this.initializeVaultSystem()
  }
}
