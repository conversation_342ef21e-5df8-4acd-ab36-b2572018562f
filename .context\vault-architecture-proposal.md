# ChatLo Vault Architecture Proposal

## Overview

This document outlines the proposed hybrid architecture for ChatLo's context vault system, with **master.md as the central intelligence hub** connecting AI and human interactions within each context.

## Core Principle: Master.md as Central Intelligence

**Key Insight**: Master.md is not just a file - it's the **living brain** of each context:
- **AI's Primary Interface**: First thing AI reads when entering a context
- **Living Memory**: Continuously updated with insights, relationships, and learnings
- **Human-AI Bridge**: Where human intent meets AI understanding
- **Context DNA**: Contains the essence and evolution of the context

## Current vs Proposed Architecture

### Current: Centralized Database
- Single SQLite database with all data
- Files treated as static attachments
- No context intelligence or memory
- Master.md not utilized as AI interface

### Proposed: Master.md-Centric Vault Architecture
- Master.md as primary AI interface and context brain
- Global search index + file-based vault storage
- Context intelligence through living master documents
- AI reads/writes to master.md for context understanding

## System Architecture

```typescript
interface ChatLoArchitecture {
  // Global system database (for search & indexing)
  systemDatabase: {
    path: string // ~/.chatlo/system.db
    tables: [
      'vault_registry',      // All vaults
      'global_search_index', // FTS index across all vaults
      'context_index',       // All contexts across vaults
      'file_index',          // File metadata (no content)
      'chat_index'           // Chat metadata
    ]
  }
  
  // Individual vault storage (file-based)
  vaultStorage: {
    path: string // ~/Documents/ChatLo-Vaults/vault-name/
    structure: [
      '.vault/metadata.json',    // Vault config
      'context-1/master.md',     // Context files
      'context-1/.context/',     // Context metadata
      'context-1/documents/'     // Actual files
    ]
  }
}
```

## Folder Structure

```
~/Documents/ChatLo-Vaults/                    # Vault Root (configurable)
├── .chatlo/                                 # System metadata
│   ├── vault-registry.json                  # All vaults index
│   ├── preferences.json                     # Global preferences
│   └── system.db                           # System-wide database
├── personal-vault/                          # Individual vault
│   ├── .vault/                             # Vault metadata
│   │   ├── metadata.json                   # Vault configuration
│   │   ├── context-index.db                # Vault-specific database
│   │   └── settings.json                   # Vault preferences
│   ├── project-alpha/                      # Context folder
│   │   ├── master.md                       # 🧠 CENTRAL INTELLIGENCE HUB
│   │   │                                   # - AI's primary interface
│   │   │                                   # - Living context memory
│   │   │                                   # - Human-AI collaboration space
│   │   │                                   # - Continuously updated by AI
│   │   ├── .context/                       # Context system files
│   │   │   ├── metadata.json               # Context configuration
│   │   │   ├── ai-memory.json              # AI's structured memory
│   │   │   ├── chat-links.json             # Linked conversations
│   │   │   └── insights/                   # AI-generated insights
│   │   │       ├── relationships.md        # File/concept relationships
│   │   │       ├── patterns.md             # Discovered patterns
│   │   │       └── evolution.md            # Context evolution log
│   │   ├── documents/                      # Context documents
│   │   ├── images/                         # Context images
│   │   └── artifacts/                      # Generated content
│   └── research-notes/                     # Another context
│       └── master.md                       # 🧠 Another intelligence hub
└── work-vault/                             # Another vault
    ├── .vault/
    └── client-project/
        └── master.md                       # 🧠 Work context intelligence
```

## Database Schema

### Global System Database

```sql
-- Vault registry (system-wide)
CREATE TABLE vaults (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  path TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT DEFAULT '#8AB0BB',
  icon TEXT DEFAULT 'folder',
  is_active INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Context index (per vault)
CREATE TABLE contexts (
  id TEXT PRIMARY KEY,
  vault_id TEXT NOT NULL,
  name TEXT NOT NULL,
  folder_path TEXT NOT NULL,
  master_doc_path TEXT,
  description TEXT,
  tags TEXT, -- JSON array
  color TEXT,
  icon TEXT,
  is_pinned INTEGER DEFAULT 0,
  file_count INTEGER DEFAULT 0,
  last_activity DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (vault_id) REFERENCES vaults (id) ON DELETE CASCADE
);

-- Global file index (metadata only, no content)
CREATE TABLE global_file_index (
  id TEXT PRIMARY KEY,
  vault_id TEXT NOT NULL,
  context_id TEXT,
  filename TEXT NOT NULL,
  filepath TEXT NOT NULL,
  relative_path TEXT NOT NULL, -- Path relative to context folder
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_hash TEXT NOT NULL,
  mime_type TEXT,
  is_processed INTEGER DEFAULT 0,
  processing_status TEXT DEFAULT 'pending', -- pending, processing, completed, failed
  last_modified TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (vault_id) REFERENCES vaults (id) ON DELETE CASCADE,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE
);

-- Full-text search index (content excerpts only)
CREATE VIRTUAL TABLE global_content_fts USING fts5(
  file_id,
  vault_id,
  context_id,
  filename,
  content_excerpt, -- First 1000 chars only
  tokenize = 'porter'
);

-- Context memory chunks (for AI optimization)
CREATE TABLE context_memory (
  id TEXT PRIMARY KEY,
  context_id TEXT NOT NULL,
  chunk_type TEXT NOT NULL, -- preferences, relationships, summary
  content TEXT NOT NULL,
  model_optimized_for TEXT, -- gemma-32k, gemma-128k, etc.
  token_count INTEGER,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE
);

-- Chat-context links
CREATE TABLE chat_context_links (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  context_id TEXT NOT NULL,
  linked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE CASCADE,
  FOREIGN KEY (context_id) REFERENCES contexts (id) ON DELETE CASCADE
);

-- Performance indexes
CREATE INDEX idx_global_files_vault ON global_file_index (vault_id);
CREATE INDEX idx_global_files_context ON global_file_index (context_id);
CREATE INDEX idx_global_files_type ON global_file_index (file_type);
CREATE INDEX idx_global_files_hash ON global_file_index (content_hash);
CREATE INDEX idx_contexts_vault ON contexts (vault_id);
CREATE INDEX idx_contexts_activity ON contexts (last_activity DESC);
CREATE INDEX idx_chat_context_links_conversation ON chat_context_links (conversation_id);
CREATE INDEX idx_chat_context_links_context ON chat_context_links (context_id);
```

### Vault-Specific Database

```sql
-- Context-specific relationships (per vault)
CREATE TABLE context_files (
  context_id TEXT NOT NULL,
  file_path TEXT NOT NULL,
  relationship_type TEXT DEFAULT 'contains',
  added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (context_id, file_path)
);

-- Chat-context links (per vault)
CREATE TABLE vault_chat_context_links (
  chat_id TEXT NOT NULL,
  context_id TEXT NOT NULL,
  linked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (chat_id, context_id)
);

-- Context activity log
CREATE TABLE context_activity (
  id TEXT PRIMARY KEY,
  context_id TEXT NOT NULL,
  activity_type TEXT NOT NULL, -- file_added, file_modified, chat_linked, etc.
  details TEXT, -- JSON
  timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## Interface Definitions

```typescript
export interface VaultConfig {
  id: string
  name: string
  path: string
  description?: string
  color?: string
  icon?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface ContextConfig {
  id: string
  vaultId: string
  name: string
  folderPath: string
  masterDocPath?: string
  description?: string
  tags?: string[]
  color?: string
  icon?: string
  isPinned: boolean
  fileCount: number
  lastActivity?: string
  createdAt: string
  updatedAt: string
}

export interface SearchOptions {
  vaultIds?: string[]
  contextIds?: string[]
  fileTypes?: string[]
  includeContent?: boolean
  deepSearch?: boolean
  limit?: number
}

export interface SearchResult {
  fileId: string
  vaultId: string
  contextId?: string
  filename: string
  filepath: string
  snippet: string
  matchType: 'filename' | 'content' | 'deep'
  relevanceScore: number
  contextName?: string
}

export interface ContextTemplate {
  name: string
  description?: string
  tags?: string[]
  color?: string
  icon?: string
  folderStructure?: string[]
  masterTemplate?: string
}
```

## Benefits

### Search Performance
- **Fast filename search** - Global index, single query
- **Medium content search** - Excerpts in FTS index  
- **Deep search on-demand** - Full content when needed
- **Scoped search** - Can limit to specific vaults/contexts

### Vault Isolation
- **Independent vault databases** - Can delete/backup individually
- **File-based storage** - Contexts remain as folders
- **Cloud sync friendly** - File structure works with Dropbox/OneDrive

### Scalability
- **Global index stays lean** - Only metadata + excerpts
- **Vault databases are small** - Only relationships
- **Content stays in files** - No database bloat

## Implementation Phases

1. **Phase 1**: Add global search index to current system
2. **Phase 2**: Implement vault structure while keeping current DB
3. **Phase 3**: Migrate to hybrid approach gradually
4. **Phase 4**: Optimize based on usage patterns

## Search Performance Comparison

| Search Type | Current (Single DB) | Hybrid Approach | Performance |
|-------------|-------------------|-----------------|-------------|
| Filename search | ✅ Fast | ✅ Fast | Same |
| Content excerpts | ❌ Slow (full content) | ✅ Fast (excerpts) | 5-10x faster |
| Deep content search | ✅ Available | ✅ On-demand | Same when needed |
| Cross-vault search | ✅ Single query | ✅ Single query | Same |
| Vault isolation | ❌ No | ✅ Yes | Better |

---

**Status**: Proposal  
**Next Steps**: Implement global search index prototype  
**Dependencies**: Current database migration system  
**Risk Level**: Medium (requires careful migration strategy)
