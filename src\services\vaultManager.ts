// Vault Manager Service
// Coordinates vault initialization and UI updates

import { VaultInitializer, VaultTemplate } from './vaultInitializer'
import { VaultUIManager } from './vaultUIManager'
import { useVaultStore } from '../stores/vaultStore'

export interface VaultManagerOptions {
  cleanExisting?: boolean
  useCustomTemplates?: boolean
  templates?: VaultTemplate[]
  showProgress?: boolean
}

export class VaultManager {
  private initializer: VaultInitializer
  private uiManager: VaultUIManager
  private currentVaultRoot: string = ''

  constructor() {
    this.initializer = new VaultInitializer('')
    this.uiManager = new VaultUIManager()
  }

  // Main function: Change vault root and reinitialize everything
  async changeVaultRoot(
    newVaultRoot: string, 
    options: VaultManagerOptions = {}
  ): Promise<{
    success: boolean
    message: string
    vaultCount: number
    contextCount: number
  }> {
    console.log('🔄 Changing vault root to:', newVaultRoot)

    try {
      // Update vault root
      this.currentVaultRoot = newVaultRoot
      this.initializer.setVaultRootPath(newVaultRoot)

      // Save vault root path to settings
      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('vaultRootPath', newVaultRoot)
        console.log('✅ Vault root path saved to settings:', newVaultRoot)

        // Also update the store if available
        try {
          const { saveVaultRootPathToDB } = require('../stores/vaultStore').useVaultStore.getState()
          if (saveVaultRootPathToDB) {
            await saveVaultRootPathToDB(newVaultRoot)
          }
        } catch (error) {
          console.warn('Could not update vault store:', error)
        }
      }

      // Step 1: Reset UI immediately
      if (options.showProgress) {
        this.showProgress('Resetting UI...')
      }
      await this.uiManager.resetUI()

      // Step 2: Clean existing structure if requested
      if (options.cleanExisting) {
        if (options.showProgress) {
          this.showProgress('Cleaning existing structure...')
        }
        await this.initializer.cleanVaultStructure()
      }

      // Step 3: Initialize new vault structure
      if (options.showProgress) {
        this.showProgress('Creating vault structure...')
      }
      
      const templates = options.useCustomTemplates && options.templates 
        ? options.templates 
        : undefined

      await this.initializer.initializeVaultStructure(templates)

      // Step 4: Load new structure into UI
      if (options.showProgress) {
        this.showProgress('Loading vault structure...')
      }
      await this.uiManager.loadVaultStructure(newVaultRoot)

      // Step 5: Get final counts
      const { vaultCount, contextCount } = await this.getStructureCounts(newVaultRoot)

      if (options.showProgress) {
        this.showProgress('Complete!')
      }

      return {
        success: true,
        message: `✅ Vault root changed successfully! Created ${vaultCount} vaults with ${contextCount} contexts.`,
        vaultCount,
        contextCount
      }

    } catch (error) {
      console.error('❌ Failed to change vault root:', error)
      return {
        success: false,
        message: `❌ Failed to change vault root: ${error instanceof Error ? error.message : 'Unknown error'}`,
        vaultCount: 0,
        contextCount: 0
      }
    }
  }

  // Quick refresh without full reinitialization
  async refreshVaultStructure(): Promise<void> {
    if (!this.currentVaultRoot) {
      throw new Error('No vault root set')
    }

    console.log('🔄 Refreshing vault structure...')
    await this.uiManager.refreshUI(this.currentVaultRoot)
  }

  // Get UI manager for component integration
  getUIManager(): VaultUIManager {
    return this.uiManager
  }

  // Get current vault root
  getCurrentVaultRoot(): string {
    return this.currentVaultRoot
  }

  // Set vault root without initialization (for loading existing)
  setVaultRoot(vaultRoot: string): void {
    this.currentVaultRoot = vaultRoot
    this.initializer.setVaultRootPath(vaultRoot)
  }

  // Load existing vault structure (no creation)
  async loadExistingStructure(vaultRoot: string): Promise<void> {
    this.setVaultRoot(vaultRoot)
    await this.uiManager.loadVaultStructure(vaultRoot)
  }

  // Create additional vault in existing structure
  async createVault(template: VaultTemplate): Promise<void> {
    if (!this.currentVaultRoot) {
      throw new Error('No vault root set')
    }

    // Use initializer to create single vault
    await this.initializer.initializeVaultStructure([template])
    
    // Refresh UI
    await this.refreshVaultStructure()
  }

  // Get structure counts
  private async getStructureCounts(vaultRoot: string): Promise<{ vaultCount: number, contextCount: number }> {
    try {
      const pathExists = await window.electronAPI.vault.pathExists(vaultRoot)
      if (!pathExists.exists) {
        return { vaultCount: 0, contextCount: 0 }
      }

      let vaultCount = 0
      let contextCount = 0

      const dirResult = await window.electronAPI.vault.readDirectory(vaultRoot)
      if (!dirResult.success) {
        return { vaultCount: 0, contextCount: 0 }
      }

      const items = dirResult.items

      for (const item of items) {
        if (item.name.startsWith('.')) continue

        if (item.isDirectory) {
          const vaultMetadataPath = this.joinPath(item.path, '.vault', 'metadata.json')
          const metadataExists = await window.electronAPI.vault.pathExists(vaultMetadataPath)

          if (metadataExists.exists) {
            vaultCount++

            // Count contexts in this vault
            const vaultDirResult = await window.electronAPI.vault.readDirectory(item.path)
            if (vaultDirResult.success) {
              for (const vaultItem of vaultDirResult.items) {
                if (vaultItem.name.startsWith('.')) continue

                if (vaultItem.isDirectory) {
                  const masterPath = this.joinPath(vaultItem.path, 'master.md')
                  const contextPath = this.joinPath(vaultItem.path, '.context')

                  const masterExists = await window.electronAPI.vault.pathExists(masterPath)
                  const contextExists = await window.electronAPI.vault.pathExists(contextPath)

                  if (masterExists.exists || contextExists.exists) {
                    contextCount++
                  }
                }
              }
            }
          }
        }
      }

      return { vaultCount, contextCount }
    } catch (error) {
      console.error('Error counting structure:', error)
      return { vaultCount: 0, contextCount: 0 }
    }
  }

  // Helper method for path joining
  private joinPath(...parts: string[]): string {
    const separator = navigator.platform.toLowerCase().includes('win') ? '\\' : '/'
    return parts.join(separator).replace(/[\/\\]+/g, separator)
  }

  // Show progress (can be overridden for custom progress display)
  private showProgress(message: string): void {
    console.log(`📊 ${message}`)
  }

  // Predefined vault templates
  static getDefaultTemplates(): VaultTemplate[] {
    return [
      {
        id: 'personal-vault',
        name: 'Personal Vault',
        description: 'Personal projects, research, and learning',
        color: '#8AB0BB',
        icon: 'user',
        contexts: [
          {
            id: 'getting-started',
            name: 'Getting Started',
            description: 'Welcome to your personal vault! Start organizing your thoughts and files here.',
            tags: ['welcome', 'tutorial', 'personal']
          },
          {
            id: 'ideas',
            name: 'Ideas & Notes',
            description: 'Capture your ideas, thoughts, and random notes',
            tags: ['ideas', 'brainstorming', 'notes']
          }
        ]
      },
      {
        id: 'work-vault',
        name: 'Work Vault',
        description: 'Professional projects and work-related contexts',
        color: '#FF8383',
        icon: 'briefcase',
        contexts: [
          {
            id: 'projects',
            name: 'Current Projects',
            description: 'Active work projects and professional development',
            tags: ['work', 'projects', 'professional']
          }
        ]
      },
      {
        id: 'learning-vault',
        name: 'Learning Vault',
        description: 'Educational content, courses, and skill development',
        color: '#1B3E68',
        icon: 'graduation-cap',
        contexts: [
          {
            id: 'courses',
            name: 'Courses & Tutorials',
            description: 'Online courses, tutorials, and learning materials',
            tags: ['learning', 'courses', 'education']
          },
          {
            id: 'research',
            name: 'Research & Studies',
            description: 'Research papers, studies, and academic content',
            tags: ['research', 'academic', 'studies']
          }
        ]
      }
    ]
  }

  // Create minimal structure (just one vault with one context)
  static getMinimalTemplates(): VaultTemplate[] {
    return [
      {
        id: 'my-vault',
        name: 'My Vault',
        description: 'Your personal context vault',
        color: '#8AB0BB',
        icon: 'folder',
        contexts: [
          {
            id: 'general',
            name: 'General',
            description: 'General purpose context for all your files and conversations',
            tags: ['general', 'default']
          }
        ]
      }
    ]
  }
}
