# Database Migration Plan: Single DB to Hybrid Vault System

## Overview

This document outlines the migration strategy from the current single database architecture to the proposed hybrid vault system, ensuring data integrity and minimal disruption.

## Current Database Schema

```sql
-- Current tables that need migration
CREATE TABLE conversations (
  id TEXT PRIMARY KEY,
  title TEXT NOT NULL,
  is_pinned INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE messages (
  id TEXT PRIMARY KEY,
  conversation_id TEXT NOT NULL,
  role TEXT NOT NULL,
  content TEXT NOT NULL,
  model TEXT,
  is_pinned INTEGER DEFAULT 0,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE files (
  id TEXT PRIMARY KEY,
  filename TEXT NOT NULL,
  filepath TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_hash TEXT NOT NULL,
  mime_type TEXT,
  extracted_content TEXT,
  metadata TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE settings (
  key TEXT PRIMARY KEY,
  value TEXT NOT NULL
);
```

## Migration Strategy

### Phase 1: Add Global Search Index (Non-Breaking)

1. **Create new search database** alongside existing database
2. **Populate global index** from existing files table
3. **Update search functionality** to use global index
4. **Test search performance** improvements

```sql
-- New search database: chatlo-search.db
CREATE TABLE global_file_index (
  id TEXT PRIMARY KEY,
  vault_id TEXT DEFAULT 'default-vault',
  context_id TEXT,
  filename TEXT NOT NULL,
  filepath TEXT NOT NULL,
  relative_path TEXT NOT NULL,
  file_type TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  content_hash TEXT NOT NULL,
  mime_type TEXT,
  is_processed INTEGER DEFAULT 0,
  processing_status TEXT DEFAULT 'pending',
  last_modified TEXT NOT NULL,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE VIRTUAL TABLE global_content_fts USING fts5(
  file_id,
  vault_id,
  context_id,
  filename,
  content_excerpt,
  tokenize = 'porter'
);
```

### Phase 2: Introduce Vault Concepts (Backward Compatible)

1. **Add vault tables** to existing database
2. **Create default vault** for existing data
3. **Update file operations** to be vault-aware
4. **Maintain backward compatibility**

```sql
-- Add to existing database
CREATE TABLE vaults (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  path TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT DEFAULT '#8AB0BB',
  icon TEXT DEFAULT 'folder',
  is_active INTEGER DEFAULT 1,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE contexts (
  id TEXT PRIMARY KEY,
  vault_id TEXT NOT NULL,
  name TEXT NOT NULL,
  folder_path TEXT NOT NULL,
  master_doc_path TEXT,
  description TEXT,
  tags TEXT,
  color TEXT,
  icon TEXT,
  is_pinned INTEGER DEFAULT 0,
  file_count INTEGER DEFAULT 0,
  last_activity DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (vault_id) REFERENCES vaults (id) ON DELETE CASCADE
);

-- Add vault_id and context_id to existing files table
ALTER TABLE files ADD COLUMN vault_id TEXT DEFAULT 'default-vault';
ALTER TABLE files ADD COLUMN context_id TEXT;
```

### Phase 3: File System Migration

1. **Create vault folder structure**
2. **Move existing files** to default vault
3. **Update file paths** in database
4. **Create context folders** for organized files

```typescript
// Migration script
async function migrateToVaultStructure() {
  // 1. Create default vault folder
  const defaultVaultPath = path.join(vaultRootPath, 'default-vault')
  fs.mkdirSync(defaultVaultPath, { recursive: true })
  
  // 2. Create default context
  const defaultContextPath = path.join(defaultVaultPath, 'general')
  fs.mkdirSync(defaultContextPath, { recursive: true })
  fs.mkdirSync(path.join(defaultContextPath, 'documents'), { recursive: true })
  fs.mkdirSync(path.join(defaultContextPath, 'images'), { recursive: true })
  
  // 3. Move existing files
  const existingFiles = db.getFiles()
  for (const file of existingFiles) {
    const newPath = moveFileToVaultStructure(file)
    db.updateFilePath(file.id, newPath)
  }
  
  // 4. Update database with vault/context info
  db.createDefaultVault()
  db.createDefaultContext()
  db.updateFilesWithVaultContext()
}
```

### Phase 4: Gradual Vault Separation

1. **Create vault-specific databases** for new vaults
2. **Keep existing data** in main database
3. **Route operations** based on vault
4. **Provide migration tools** for users

## Migration Implementation

### Database Version Management

```typescript
// Update database manager with migration support
export class DatabaseManager {
  private currentVersion = 6 // Increment from current version 5
  
  private runMigrations(): void {
    const version = this.getVersion()
    
    // Migration v5 -> v6: Add vault support
    if (version < 6) {
      console.log('Migration v6: Adding vault support')
      this.migrateToVaultSupport()
    }
  }
  
  private migrateToVaultSupport(): void {
    this.db.exec(`
      -- Add vault tables
      CREATE TABLE IF NOT EXISTS vaults (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        description TEXT,
        color TEXT DEFAULT '#8AB0BB',
        icon TEXT DEFAULT 'folder',
        is_active INTEGER DEFAULT 1,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      );
      
      CREATE TABLE IF NOT EXISTS contexts (
        id TEXT PRIMARY KEY,
        vault_id TEXT NOT NULL,
        name TEXT NOT NULL,
        folder_path TEXT NOT NULL,
        master_doc_path TEXT,
        description TEXT,
        tags TEXT,
        color TEXT,
        icon TEXT,
        is_pinned INTEGER DEFAULT 0,
        file_count INTEGER DEFAULT 0,
        last_activity DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (vault_id) REFERENCES vaults (id) ON DELETE CASCADE
      );
      
      -- Add vault columns to existing files table
      ALTER TABLE files ADD COLUMN vault_id TEXT DEFAULT 'default-vault';
      ALTER TABLE files ADD COLUMN context_id TEXT;
      
      -- Create default vault
      INSERT INTO vaults (id, name, path, description) 
      VALUES ('default-vault', 'Default Vault', '${this.getDefaultVaultPath()}', 'Migrated from single folder structure');
      
      -- Create default context
      INSERT INTO contexts (id, vault_id, name, folder_path, description)
      VALUES ('default-context', 'default-vault', 'General', '${this.getDefaultContextPath()}', 'General files and documents');
      
      -- Update existing files with default vault/context
      UPDATE files SET vault_id = 'default-vault', context_id = 'default-context';
      
      -- Add indexes
      CREATE INDEX IF NOT EXISTS idx_files_vault ON files (vault_id);
      CREATE INDEX IF NOT EXISTS idx_files_context ON files (context_id);
      CREATE INDEX IF NOT EXISTS idx_contexts_vault ON contexts (vault_id);
    `)
    
    // Initialize global search index
    this.initializeGlobalSearchIndex()
  }
  
  private initializeGlobalSearchIndex(): void {
    // Create search database and populate from existing files
    const searchService = new GlobalSearchService()
    
    // Migrate existing files to global index
    const files = this.getFiles()
    for (const file of files) {
      searchService.updateFileIndex(file, 'default-vault', 'default-context')
      
      if (file.extracted_content) {
        searchService.indexFileContent(
          file.id, 
          'default-vault', 
          'default-context', 
          file.filename, 
          file.extracted_content
        )
      }
    }
  }
}
```

### User Experience During Migration

1. **Transparent migration** - Users see no difference initially
2. **Progressive enhancement** - New features become available gradually
3. **Data safety** - Original database backed up before migration
4. **Rollback capability** - Can revert if issues occur

### Testing Strategy

1. **Unit tests** for migration functions
2. **Integration tests** for search functionality
3. **Performance benchmarks** before/after migration
4. **User acceptance testing** with real data

## Risk Mitigation

### Data Safety
- **Automatic backups** before each migration step
- **Validation checks** after each migration
- **Rollback procedures** if migration fails

### Performance
- **Gradual index building** to avoid blocking UI
- **Background processing** for content indexing
- **Progress indicators** for long operations

### Compatibility
- **Backward compatibility** maintained during transition
- **Feature flags** to enable/disable new functionality
- **Graceful degradation** if new features fail

## Timeline

- **Week 1-2**: Implement global search index (Phase 1)
- **Week 3-4**: Add vault concepts to existing DB (Phase 2)
- **Week 5-6**: File system migration (Phase 3)
- **Week 7-8**: Vault separation and testing (Phase 4)

## Success Criteria

- ✅ All existing data migrated successfully
- ✅ Search performance improved by 5-10x
- ✅ No data loss during migration
- ✅ Backward compatibility maintained
- ✅ New vault features working correctly
- ✅ User experience remains smooth

---

**Status**: Planning  
**Risk Level**: Medium  
**Dependencies**: Current database schema, file system structure  
**Rollback Plan**: Restore from backup, disable new features
