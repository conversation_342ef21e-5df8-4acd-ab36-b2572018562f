{"contextId": "project-alpha-001", "createdAt": "2025-01-15T10:00:00Z", "lastUpdated": "2025-01-21T14:30:00Z", "insights": [{"id": "insight-001", "type": "pattern", "content": "Button components consistently use design tokens for spacing and colors", "confidence": 0.95, "discoveredAt": "2025-01-21T12:00:00Z"}, {"id": "insight-002", "type": "relationship", "content": "Design tokens file is referenced by all component documentation", "confidence": 0.88, "discoveredAt": "2025-01-20T15:30:00Z"}], "patterns": [{"id": "pattern-001", "name": "Token-based theming", "description": "All components use centralized design tokens", "frequency": 0.92, "examples": ["buttons.md", "forms.md", "navigation.md"]}], "relationships": [{"source": "design-tokens.json", "target": "components/buttons.md", "type": "dependency", "strength": 0.9}], "conversationSummaries": [{"chatId": "chat-001", "summary": "Discussion about button accessibility improvements", "keyTopics": ["accessibility", "WCAG", "button states"], "date": "2025-01-21T10:00:00Z"}]}