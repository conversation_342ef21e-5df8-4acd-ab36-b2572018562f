import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'
import { useAppStore } from '../store'
import { useVaultStore } from '../stores/vaultStore'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFolder,
  faFolderOpen,
  faFileText,
  faFileCode,
  faFilePdf,
  faFileWord,
  faFileExcel,
  faFilePowerpoint,
  faFileImage,
  faFileVideo,
  faFileAudio,
  faFileZipper,
  faFile,
  faChevronDown,
  faChevronUp,
  faFolderTree,
  faPlus,
  faSitemap,
  faClock,
  faUser,
  faDatabase
} from '@fortawesome/free-solid-svg-icons'

interface FileTreeNode {
  type: 'folder' | 'file'
  name: string
  path: string
  isExpanded?: boolean
  isSelected?: boolean
  children?: FileTreeNode[]
  fileCount?: number
  icon: any // FontAwesome IconDefinition
  color: string
}

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}

interface VaultInfo {
  id: string
  name: string
  path: string
  contextCount: number
  lastActivity: string
  color: string
  icon: string
}

interface ContextInfo {
  id: string
  name: string
  vaultId: string
  fileCount: number
  chatCount: number
  lastActivity: string
  description: string
}

// Helper function to get file type icon and color
const getFileTypeIcon = (fileName: string, fileType: string) => {
  const extension = fileName.split('.').pop()?.toLowerCase()

  if (fileType === 'Folder') {
    return { icon: faFolder, color: 'text-supplement2', bgColor: 'bg-supplement2/20' }
  }

  switch (extension) {
    case 'md':
    case 'markdown':
      return { icon: faFileText, color: 'text-primary', bgColor: 'bg-primary/20' }
    case 'json':
      return { icon: faFileCode, color: 'text-yellow-400', bgColor: 'bg-yellow-500/20' }
    case 'js':
    case 'jsx':
    case 'ts':
    case 'tsx':
      return { icon: faFileCode, color: 'text-blue-400', bgColor: 'bg-blue-500/20' }
    case 'pdf':
      return { icon: faFilePdf, color: 'text-red-400', bgColor: 'bg-red-500/20' }
    case 'doc':
    case 'docx':
      return { icon: faFileWord, color: 'text-blue-600', bgColor: 'bg-blue-600/20' }
    case 'xls':
    case 'xlsx':
      return { icon: faFileExcel, color: 'text-green-400', bgColor: 'bg-green-500/20' }
    case 'ppt':
    case 'pptx':
      return { icon: faFilePowerpoint, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    case 'png':
    case 'jpg':
    case 'jpeg':
    case 'gif':
    case 'svg':
    case 'webp':
      return { icon: faFileImage, color: 'text-purple-400', bgColor: 'bg-purple-500/20' }
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
      return { icon: faFileVideo, color: 'text-pink-400', bgColor: 'bg-pink-500/20' }
    case 'mp3':
    case 'wav':
    case 'flac':
      return { icon: faFileAudio, color: 'text-indigo-400', bgColor: 'bg-indigo-500/20' }
    case 'zip':
    case 'rar':
    case '7z':
      return { icon: faFileZipper, color: 'text-gray-400', bgColor: 'bg-gray-500/20' }
    case 'css':
      return { icon: faFileCode, color: 'text-cyan-400', bgColor: 'bg-cyan-500/20' }
    case 'html':
    case 'htm':
      return { icon: faFileCode, color: 'text-orange-400', bgColor: 'bg-orange-500/20' }
    default:
      return { icon: faFile, color: 'text-supplement1', bgColor: 'bg-supplement1/20' }
  }
}

const FilesPage: React.FC = () => {
  const navigate = useNavigate()
  const { artifactsVisible = false } = useAppStore() || {}

  // Use vault store for global state
  const {
    vaults,
    contexts: storeContexts,
    activeVaultId,
    setActiveVaultId,
    loadActiveVaultFromDB
  } = useVaultStore()

  // Set up state variables
  const [selectedVaultId, setSelectedVaultId] = useState(activeVaultId || '')
  const [selectedContextId, setSelectedContextId] = useState('')
  const [masterContent, setMasterContent] = useState<string>('')
  const [isLoadingMaster, setIsLoadingMaster] = useState(false)

  // Load active vault from database on mount
  useEffect(() => {
    loadActiveVaultFromDB()
  }, [])

  // Set default selections when vaults/contexts are loaded
  useEffect(() => {
    if (vaults.length > 0 && !selectedVaultId) {
      const defaultVault = activeVaultId ? vaults.find(v => v.id === activeVaultId) : vaults[0]
      if (defaultVault) {
        setSelectedVaultId(defaultVault.id)
      }
    }
  }, [vaults, activeVaultId, selectedVaultId])

  useEffect(() => {
    if (storeContexts.length > 0 && selectedVaultId && !selectedContextId) {
      const vaultContexts = storeContexts.filter(c => c.vaultId === selectedVaultId)
      if (vaultContexts.length > 0) {
        setSelectedContextId(vaultContexts[0].id)
      }
    }
  }, [storeContexts, selectedVaultId, selectedContextId])

  // Load master.md content when context changes
  const loadMasterContent = async (contextId: string, vaultId: string) => {
    if (!contextId || !vaultId) return

    setIsLoadingMaster(true)
    try {
      const vault = vaults.find(v => v.id === vaultId)
      const context = storeContexts.find(c => c.id === contextId)

      if (vault && context) {
        // Construct path to master.md
        const masterPath = `${vault.path}/${context.name}/master.md`

        // Check if file exists
        const pathExists = await window.electronAPI.vault.pathExists(masterPath)
        if (pathExists.exists) {
          // Read file content
          const content = await window.electronAPI.files.getFileContent(masterPath)
          setMasterContent(content ? content.toString() : '# Master Document\n\nNo content available.')
        } else {
          setMasterContent('# Master Document\n\nThis context vault is empty. Add files to get started.')
        }
      }
    } catch (error) {
      console.error('Error loading master.md:', error)
      setMasterContent('# Error\n\nFailed to load master document.')
    } finally {
      setIsLoadingMaster(false)
    }
  }

  // Load master content when context changes
  useEffect(() => {
    if (selectedContextId && selectedVaultId) {
      loadMasterContent(selectedContextId, selectedVaultId)
    }
  }, [selectedContextId, selectedVaultId, vaults, storeContexts])

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])
  
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'master', // Default to master mode as shown in design
    showArtifacts: false,
    artifactsExpanded: false
  })
  
  const [selectedFile, setSelectedFile] = useState<string | null>('master.md')
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set(['root', 'documents']))

  // Get current vault and context
  const currentContext = storeContexts.find(c => c.id === selectedContextId)
  const availableContexts = storeContexts.filter(c => c.vaultId === selectedVaultId)

  // Generate file tree based on selected context
  const getFileTreeForContext = (contextId: string): FileTreeNode[] => {
    const context = storeContexts.find(c => c.id === contextId)
    if (!context) return []

    const baseTree: FileTreeNode[] = [
      {
        type: 'file',
        name: 'master.md',
        path: `/${contextId}/master.md`,
        isSelected: selectedFile === 'master.md',
        icon: faFileText,
        color: 'text-primary'
      },
      {
        type: 'folder',
        name: '.context',
        path: `/${contextId}/.context`,
        isExpanded: false,
        fileCount: 3,
        icon: faFolder,
        color: 'text-gray-500',
        children: [
          {
            type: 'file',
            name: 'ai-memory.json',
            path: `/${contextId}/.context/ai-memory.json`,
            icon: faFileCode,
            color: 'text-yellow-400'
          },
          {
            type: 'folder',
            name: 'insights',
            path: `/${contextId}/.context/insights`,
            isExpanded: false,
            fileCount: 3,
            icon: faFolder,
            color: 'text-gray-500',
            children: []
          }
        ]
      },
      {
        type: 'folder',
        name: 'documents',
        path: `/${contextId}/documents`,
        isExpanded: true,
        fileCount: contextId === 'project-alpha' ? 4 : contextId === 'research-notes' ? 8 : 6,
        icon: faFolderOpen,
        color: 'text-supplement2',
        children: contextId === 'project-alpha' ? [
          {
            type: 'file',
            name: 'design-tokens.json',
            path: `/${contextId}/documents/design-tokens.json`,
            icon: faFileCode,
            color: 'text-yellow-400'
          },
          {
            type: 'folder',
            name: 'components',
            path: `/${contextId}/documents/components`,
            isExpanded: true,
            fileCount: 2,
            icon: faFolderOpen,
            color: 'text-supplement2',
            children: [
              {
                type: 'file',
                name: 'buttons.md',
                path: `/${contextId}/documents/components/buttons.md`,
                icon: faFileText,
                color: 'text-secondary'
              },
              {
                type: 'file',
                name: 'forms.md',
                path: `/${contextId}/documents/components/forms.md`,
                icon: faFileText,
                color: 'text-gray-400'
              }
            ]
          }
        ] : contextId === 'research-notes' ? [
          {
            type: 'file',
            name: 'user-study-2025.pdf',
            path: `/${contextId}/documents/user-study-2025.pdf`,
            icon: faFilePdf,
            color: 'text-red-400'
          },
          {
            type: 'file',
            name: 'competitor-analysis.md',
            path: `/${contextId}/documents/competitor-analysis.md`,
            icon: faFileText,
            color: 'text-supplement1'
          }
        ] : [
          {
            type: 'file',
            name: 'requirements.md',
            path: `/${contextId}/documents/requirements.md`,
            icon: faFileText,
            color: 'text-supplement1'
          },
          {
            type: 'file',
            name: 'wireframes.fig',
            path: `/${contextId}/documents/wireframes.fig`,
            icon: faFile,
            color: 'text-purple-400'
          }
        ]
      },
      {
        type: 'folder',
        name: 'images',
        path: `/${contextId}/images`,
        isExpanded: false,
        fileCount: 3,
        icon: faFolder,
        color: 'text-supplement2',
        children: []
      },
      {
        type: 'folder',
        name: 'artifacts',
        path: `/${contextId}/artifacts`,
        isExpanded: false,
        fileCount: 5,
        icon: faFolder,
        color: 'text-supplement2',
        children: []
      }
    ]

    return [{
      type: 'folder',
      name: context.name,
      path: `/${contextId}`,
      isExpanded: true,
      fileCount: context.fileCount,
      icon: faFolderOpen,
      color: 'text-supplement2',
      children: baseTree
    }]
  }

  const [fileTree, setFileTree] = useState<FileTreeNode[]>(getFileTreeForContext(selectedContextId))

  // Update file tree when context changes
  useEffect(() => {
    setFileTree(getFileTreeForContext(selectedContextId))
  }, [selectedContextId])

  const handleVaultChange = (vaultId: string) => {
    setSelectedVaultId(vaultId)
    // Update global active vault
    setActiveVaultId(vaultId)
    // Select first context in the new vault
    const firstContext = storeContexts.find(c => c.vaultId === vaultId)
    if (firstContext) {
      setSelectedContextId(firstContext.id)
    }
  }

  const handleContextChange = (contextId: string) => {
    setSelectedContextId(contextId)
    setSelectedFile('master.md') // Always select master.md when switching contexts
  }

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)
  }

  const handleModeChange = (mode: 'explorer' | 'master') => {
    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))

    // Auto-select master.md when switching to master mode
    if (mode === 'master') {
      setSelectedFile('master.md')
    }
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path)
            } else {
              selectFile(node.path)
            }
          }}
        >
          {node.type === 'folder' && (
            <FontAwesomeIcon
              icon={isExpanded ? faChevronDown : faChevronRight}
              className="text-gray-400 text-xs w-3"
            />
          )}
          {node.type === 'file' && <div className="w-3"></div>}

          <FontAwesomeIcon icon={node.icon} className={`text-sm ${node.color}`} />
          
          <span className={`text-sm ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}>
            {node.name}
          </span>
          
          <div className="ml-auto">
            {node.fileCount && (
              <span className={`w-5 h-5 ${node.color === 'text-secondary' ? 'bg-secondary/20 text-secondary' : 'bg-supplement2/20 text-supplement2'} text-xs rounded-full flex items-center justify-center font-medium`}>
                {node.fileCount}
              </span>
            )}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  // Show empty state if no vaults are available
  if (vaults.length === 0) {
    return (
      <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
        {/* Artifacts Sidebar */}
        {artifactsVisible && <ArtifactsSidebar />}

        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <div className="w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center mx-auto mb-4">
              <FontAwesomeIcon icon={faDatabase} className="text-primary text-2xl" />
            </div>
            <h2 className="text-xl font-semibold text-supplement1 mb-2">No Vaults Found</h2>
            <p className="text-gray-400 mb-6">
              Create your first context vault to start organizing your files and conversations.
            </p>
            <button
              onClick={() => navigate('/')}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/80 transition-colors"
            >
              Go to Home
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Artifacts Sidebar */}
      {artifactsVisible && <ArtifactsSidebar />}

      {/* Main Files Content */}
      <div className="flex-1 flex bg-gray-900">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col">
          
          {/* Vault Selector */}
          <div className="p-4 border-b border-tertiary/50">
            <VaultDropdown
              vaults={vaults}
              selectedVaultId={selectedVaultId}
              onVaultSelect={handleVaultChange}
              onViewAll={() => navigate('/')}
            />
          </div>

          {/* Context Selector */}
          {selectedVaultId && availableContexts.length > 0 && (
            <div className="p-3 border-b border-tertiary/50">
              <div className="text-xs text-gray-400 mb-2">Context</div>
              <select
                value={selectedContextId}
                onChange={(e) => handleContextChange(e.target.value)}
                className="w-full bg-gray-700 border border-tertiary/30 rounded px-2 py-1 text-sm text-supplement1 focus:outline-none focus:border-primary/50"
              >
                <option value="">Select Context</option>
                {availableContexts.map(context => (
                  <option key={context.id} value={context.id}>
                    {context.name}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* File Tree Header */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faFolderTree} className="text-supplement2 text-sm" />
                <h3 className="font-medium text-supplement1 text-sm">
                  {currentContext?.name || 'Context Files'}
                </h3>
              </div>
              <button className="p-1 hover:bg-gray-700 rounded transition-colors group relative">
                <FontAwesomeIcon icon={faPlus} className="text-gray-400 text-xs" />
                <div className="absolute bottom-6 right-0 bg-gray-700 text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                  Add File
                </div>
              </button>
            </div>
          </div>

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faSitemap} className="text-sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faLightbulb} className="text-sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree */}
          <div className="flex-1 overflow-y-auto p-2">
            {fileTree.map(node => renderFileTreeNode(node))}
          </div>
        </div>
        
        {/* Right Column Content */}
        {!selectedContextId ? (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center max-w-md">
              <div className="w-12 h-12 bg-supplement2/20 rounded-lg flex items-center justify-center mx-auto mb-4">
                <FontAwesomeIcon icon={faFolder} className="text-supplement2 text-xl" />
              </div>
              <h3 className="text-lg font-semibold text-supplement1 mb-2">Select a Context</h3>
              <p className="text-gray-400 mb-4">
                Choose a context from the dropdown above to view its files and master document.
              </p>
              {availableContexts.length === 0 && selectedVaultId && (
                <p className="text-sm text-gray-500">
                  No contexts found in this vault. Create a new context to get started.
                </p>
              )}
            </div>
          </div>
        ) : viewMode.currentMode === 'master' ? (
          <MasterMode
            context={currentContext}
            content={masterContent}
            isLoading={isLoadingMaster}
          />
        ) : (
          <ExplorerMode />
        )}
      </div>
    </div>
  )
}

// Master Mode Component
const MasterMode: React.FC<{
  context?: ContextInfo
  content: string
  isLoading: boolean
}> = ({ context, content, isLoading }) => {
  const getMasterMetadata = (context?: ContextInfo) => {
    return {
      title: context?.name || 'Master Document',
      description: context?.description || 'Context overview and AI collaboration space.',
      lastModified: context?.lastActivity || 'Recently',
      size: `${Math.round(content.length / 1024)} KB`,
      author: 'Context Vault'
    }
  }

  const masterMetadata = getMasterMetadata(context)

  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="markdown-content">
          {/* Document Header */}
          <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
            <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
              <FontAwesomeIcon icon={faFileText} className="text-primary text-lg" />
            </div>
            <div>
              <h1 className="text-supplement1 text-xl font-semibold">{masterMetadata.title}</h1>
              <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faClock} className="text-xs" />
                  Modified {masterMetadata.lastModified}
                </span>
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faFileText} className="text-xs" />
                  {masterMetadata.size}
                </span>
                <span className="flex items-center gap-1">
                  <FontAwesomeIcon icon={faUser} className="text-xs" />
                  {masterMetadata.author}
                </span>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-gray-400">Loading master document...</div>
            </div>
          ) : (
            <div className="prose prose-invert max-w-none">
              <div
                className="text-gray-300 text-sm leading-relaxed"
                dangerouslySetInnerHTML={{
                  __html: content
                    .replace(/^# (.*$)/gm, '<h1 class="text-xl font-bold text-supplement1 mb-4 mt-6">$1</h1>')
                    .replace(/^## (.*$)/gm, '<h2 class="text-lg font-semibold text-supplement1 mb-3 mt-5">$1</h2>')
                    .replace(/^### (.*$)/gm, '<h3 class="text-base font-medium text-supplement2 mb-2 mt-4">$1</h3>')
                    .replace(/^\*\*(.*?)\*\*/gm, '<strong class="text-supplement1">$1</strong>')
                    .replace(/^\*(.*?)\*/gm, '<em class="text-gray-300">$1</em>')
                    .replace(/^- (.*$)/gm, '<li class="ml-4 mb-1">• $1</li>')
                    .replace(/\n/g, '<br>')
                }}
              />
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Explorer Mode Component (placeholder)
const ExplorerMode: React.FC = () => {
  return (
    <div className="flex-1 flex items-center justify-center">
      <div className="text-gray-400">Explorer mode - Coming soon</div>
    </div>
  )
}

// Vault Dropdown Component
interface VaultDropdownProps {
  vaults: VaultInfo[]
  selectedVaultId: string
  onVaultSelect: (vaultId: string) => void
  onViewAll: () => void
}

const VaultDropdown: React.FC<VaultDropdownProps> = ({
  vaults,
  selectedVaultId,
  onVaultSelect,
  onViewAll
}) => {
  const [showDropdown, setShowDropdown] = useState(false)
  const selectedVault = vaults.find(v => v.id === selectedVaultId)

  return (
    <div className="relative">
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="flex items-center gap-2 px-3 py-2 bg-gray-800/50 rounded-lg border border-tertiary/30 hover:border-primary/30 transition-colors"
      >
        <div className="w-6 h-6 bg-primary/20 rounded flex items-center justify-center">
          <FontAwesomeIcon icon={faDatabase} className="text-primary text-xs" />
        </div>
        <span className="text-sm text-supplement1 font-medium">
          {selectedVault?.name || 'Select Vault'}
        </span>
        <FontAwesomeIcon
          icon={showDropdown ? faChevronUp : faChevronDown}
          className="text-gray-400 text-xs ml-auto"
        />
      </button>

      {showDropdown && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-gray-800 rounded-lg border border-tertiary/30 shadow-lg z-10">
          {/* Vault List */}
          <div className="max-h-48 overflow-y-auto">
            {vaults.map(vault => (
              <button
                key={vault.id}
                onClick={() => {
                  onVaultSelect(vault.id)
                  setShowDropdown(false)
                }}
                className={`w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-700/50 transition-colors ${
                  vault.id === selectedVaultId ? 'bg-primary/10' : ''
                }`}
              >
                <div className="w-6 h-6 bg-primary/20 rounded flex items-center justify-center">
                  <FontAwesomeIcon icon={faDatabase} className="text-primary text-xs" />
                </div>
                <div className="flex-1">
                  <div className="text-sm text-supplement1 font-medium">{vault.name}</div>
                  <div className="text-xs text-gray-400">{vault.contextCount} contexts</div>
                </div>
              </button>
            ))}
          </div>

          {/* View All Link */}
          <div className="p-3">
            <button
              onClick={() => {
                onViewAll()
                setShowDropdown(false)
              }}
              className="text-sm text-primary hover:text-primary/80 transition-colors"
            >
              View All Vaults
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default FilesPage
