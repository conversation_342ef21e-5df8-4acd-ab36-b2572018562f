// Vault Store - Global state management for vault selection
import { create } from 'zustand'

export interface VaultInfo {
  id: string
  name: string
  path: string
  contextCount: number
  lastActivity: string
  color: string
  icon: string
  size: string
}

export interface ContextInfo {
  id: string
  name: string
  vaultId: string
  fileCount: number
  chatCount: number
  lastActivity: string
  description: string
}

interface VaultStore {
  // State
  vaults: VaultInfo[]
  contexts: ContextInfo[]
  activeVaultId: string | null
  vaultRootPath: string
  isLoading: boolean

  // Actions
  setVaults: (vaults: VaultInfo[]) => void
  setContexts: (contexts: ContextInfo[]) => void
  setActiveVaultId: (vaultId: string) => void
  setVaultRootPath: (path: string) => void
  setLoading: (loading: boolean) => void
  
  // Computed
  getActiveVault: () => VaultInfo | null
  getVaultContexts: (vaultId: string) => ContextInfo[]
  
  // Persistence
  loadActiveVaultFromDB: () => Promise<void>
  saveActiveVaultToDB: (vaultId: string) => Promise<void>
}

export const useVaultStore = create<VaultStore>((set, get) => ({
  // Initial state
  vaults: [],
  contexts: [],
  activeVaultId: null,
  vaultRootPath: '',
  isLoading: false,

  // Actions
  setVaults: (vaults) => set({ vaults }),
  setContexts: (contexts) => set({ contexts }),
  setActiveVaultId: (vaultId) => {
    set({ activeVaultId: vaultId })
    // Auto-save to database
    get().saveActiveVaultToDB(vaultId)
  },
  setVaultRootPath: (path) => set({ vaultRootPath: path }),
  setLoading: (loading) => set({ isLoading: loading }),

  // Computed getters
  getActiveVault: () => {
    const { vaults, activeVaultId } = get()
    return vaults.find(vault => vault.id === activeVaultId) || null
  },

  getVaultContexts: (vaultId) => {
    const { contexts } = get()
    return contexts.filter(context => context.vaultId === vaultId)
  },

  // Database persistence
  loadActiveVaultFromDB: async () => {
    try {
      if (window.electronAPI?.settings) {
        const savedVaultId = await window.electronAPI.settings.get('activeVaultId')
        if (savedVaultId) {
          set({ activeVaultId: savedVaultId })
        }

        // Also load vault root path
        const savedVaultRootPath = await window.electronAPI.settings.get('vaultRootPath')
        if (savedVaultRootPath) {
          set({ vaultRootPath: savedVaultRootPath })
          console.log('✅ Loaded vault root path from settings:', savedVaultRootPath)
        }
      }
    } catch (error) {
      console.error('Error loading active vault from DB:', error)
    }
  },

  saveActiveVaultToDB: async (vaultId) => {
    try {
      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('activeVaultId', vaultId)
        console.log('Active vault saved to DB:', vaultId)
      }
    } catch (error) {
      console.error('Error saving active vault to DB:', error)
    }
  },

  saveVaultRootPathToDB: async (path) => {
    try {
      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('vaultRootPath', path)
        set({ vaultRootPath: path })
        console.log('Vault root path saved to DB:', path)
      }
    } catch (error) {
      console.error('Error saving vault root path to DB:', error)
    }
  }
}))

// Helper hook for active vault
export const useActiveVault = () => {
  const activeVaultId = useVaultStore(state => state.activeVaultId)
  const getActiveVault = useVaultStore(state => state.getActiveVault)
  return {
    activeVaultId,
    activeVault: getActiveVault()
  }
}

// Helper hook for vault contexts
export const useVaultContexts = (vaultId?: string) => {
  const getVaultContexts = useVaultStore(state => state.getVaultContexts)
  const activeVaultId = useVaultStore(state => state.activeVaultId)
  
  const targetVaultId = vaultId || activeVaultId
  return targetVaultId ? getVaultContexts(targetVaultId) : []
}
