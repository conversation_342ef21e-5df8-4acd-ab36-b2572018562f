import React, { useState, useEffect } from 'react'
import { useVaultStore } from '../stores/vaultStore'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faChevronDown, faFolder, faFolderOpen, faPlus } from '@fortawesome/free-solid-svg-icons'

interface ContextVaultSelectorProps {
  selectedContextId: string | null
  onContextChange: (contextId: string | null) => void
  className?: string
}

const ContextVaultSelector: React.FC<ContextVaultSelectorProps> = ({
  selectedContextId,
  onContextChange,
  className = ''
}) => {
  const { vaults, contexts, activeVaultId } = useVaultStore()
  const [isOpen, setIsOpen] = useState(false)
  const [selectedVaultId, setSelectedVaultId] = useState(activeVaultId)

  // Get available contexts for the selected vault
  const availableContexts = contexts.filter(context => 
    selectedVaultId ? context.vaultId === selectedVaultId : true
  )

  // Get current vault and context
  const currentVault = vaults.find(v => v.id === selectedVaultId)
  const currentContext = contexts.find(c => c.id === selectedContextId)

  // Update selected vault when active vault changes
  useEffect(() => {
    if (activeVaultId && activeVaultId !== selectedVaultId) {
      setSelectedVaultId(activeVaultId)
    }
  }, [activeVaultId])

  const handleContextSelect = (contextId: string | null) => {
    onContextChange(contextId)
    setIsOpen(false)
  }

  const handleVaultChange = (vaultId: string) => {
    setSelectedVaultId(vaultId)
    // Reset context selection when vault changes
    onContextChange(null)
  }

  const getDisplayText = () => {
    if (!selectedContextId) {
      return 'No Context Vault Selected'
    }
    
    if (currentContext && currentVault) {
      return `${currentVault.name} / ${currentContext.name}`
    }
    
    return 'Unknown Context'
  }

  const getDisplayIcon = () => {
    if (!selectedContextId) {
      return faFolder
    }
    return faFolderOpen
  }

  return (
    <div className={`relative ${className}`}>
      {/* Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg hover:bg-gray-700 transition-colors text-sm w-full max-w-xs"
      >
        <FontAwesomeIcon 
          icon={getDisplayIcon()} 
          className={`text-sm ${selectedContextId ? 'text-primary' : 'text-gray-400'}`} 
        />
        <span className={`flex-1 text-left truncate ${selectedContextId ? 'text-supplement1' : 'text-gray-400'}`}>
          {getDisplayText()}
        </span>
        <FontAwesomeIcon 
          icon={faChevronDown} 
          className={`text-xs text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
          
          {/* Menu */}
          <div className="absolute top-full left-0 mt-1 w-full max-w-xs bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto">
            {/* No Context Option */}
            <button
              onClick={() => handleContextSelect(null)}
              className={`w-full flex items-center gap-3 px-3 py-2 text-left hover:bg-gray-700 transition-colors ${
                !selectedContextId ? 'bg-gray-700/50' : ''
              }`}
            >
              <FontAwesomeIcon icon={faFolder} className="text-gray-400 text-sm" />
              <span className="text-gray-400 text-sm">No Context Vault Selected</span>
            </button>

            {/* Vault Sections */}
            {vaults.map((vault) => {
              const vaultContexts = contexts.filter(c => c.vaultId === vault.id)
              
              if (vaultContexts.length === 0) return null

              return (
                <div key={vault.id} className="border-t border-gray-700 first:border-t-0">
                  {/* Vault Header */}
                  <div className="px-3 py-2 bg-gray-750 text-xs text-gray-400 font-medium">
                    {vault.name} ({vaultContexts.length} contexts)
                  </div>
                  
                  {/* Vault Contexts */}
                  {vaultContexts.map((context) => (
                    <button
                      key={context.id}
                      onClick={() => handleContextSelect(context.id)}
                      className={`w-full flex items-center gap-3 px-6 py-2 text-left hover:bg-gray-700 transition-colors ${
                        selectedContextId === context.id ? 'bg-primary/20 border-l-2 border-primary' : ''
                      }`}
                    >
                      <FontAwesomeIcon 
                        icon={faFolderOpen} 
                        className={`text-sm ${selectedContextId === context.id ? 'text-primary' : 'text-supplement2'}`} 
                      />
                      <div className="flex-1 min-w-0">
                        <div className={`text-sm truncate ${
                          selectedContextId === context.id ? 'text-primary' : 'text-supplement1'
                        }`}>
                          {context.name}
                        </div>
                        <div className="text-xs text-gray-400 truncate">
                          {context.description || `${context.fileCount} files`}
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )
            })}

            {/* Empty State */}
            {vaults.length === 0 && (
              <div className="px-3 py-4 text-center text-gray-400 text-sm">
                <FontAwesomeIcon icon={faFolder} className="text-2xl mb-2 block" />
                <p>No vaults available</p>
                <p className="text-xs mt-1">Create a vault in Settings</p>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}

export default ContextVaultSelector
