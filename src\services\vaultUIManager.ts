// Vault UI Manager Service
// Manages UI state when vault structure changes

import { useVaultStore } from '../stores/vaultStore'

export interface UIVaultInfo {
  id: string
  name: string
  path: string
  contextCount: number
  lastActivity: string
  color: string
  icon: string
  size: string
}

export interface UIContextInfo {
  id: string
  name: string
  vaultId: string
  fileCount: number
  chatCount: number
  lastActivity: string
  description: string
}

export class VaultUIManager {
  private onVaultsUpdated?: (vaults: UIVaultInfo[]) => void
  private onContextsUpdated?: (contexts: UIContextInfo[]) => void
  private onActiveVaultChanged?: (vaultId: string) => void

  // Register callbacks for UI updates
  onVaultsChange(callback: (vaults: UIVaultInfo[]) => void): void {
    this.onVaultsUpdated = callback
  }

  onContextsChange(callback: (contexts: UIContextInfo[]) => void): void {
    this.onContextsUpdated = callback
  }

  onActiveVaultChange(callback: (vaultId: string) => void): void {
    this.onActiveVaultChanged = callback
  }

  // Reset UI to empty state
  async resetUI(): Promise<void> {
    console.log('🔄 Resetting UI state...')
    
    // Clear vaults
    if (this.onVaultsUpdated) {
      this.onVaultsUpdated([])
    }

    // Clear contexts
    if (this.onContextsUpdated) {
      this.onContextsUpdated([])
    }

    // Clear active vault
    if (this.onActiveVaultChanged) {
      this.onActiveVaultChanged('')
    }

    console.log('✅ UI state reset')
  }

  // Load and update UI with new vault structure
  async loadVaultStructure(vaultRootPath: string): Promise<void> {
    console.log('📂 Loading vault structure from:', vaultRootPath)

    try {
      // Scan for vaults
      const vaults = await this.scanVaults(vaultRootPath)
      const contexts = await this.scanContexts(vaultRootPath, vaults)

      // Update vault store directly
      const vaultStore = useVaultStore.getState()
      vaultStore.setVaults(vaults)
      vaultStore.setContexts(contexts)
      vaultStore.setVaultRootPath(vaultRootPath)

      // Set first vault as active if none is set
      if (vaults.length > 0 && !vaultStore.activeVaultId) {
        vaultStore.setActiveVaultId(vaults[0].id)
      }

      // Also call callbacks for backward compatibility
      if (this.onVaultsUpdated) {
        this.onVaultsUpdated(vaults)
      }

      if (this.onContextsUpdated) {
        this.onContextsUpdated(contexts)
      }

      if (vaults.length > 0 && this.onActiveVaultChanged) {
        this.onActiveVaultChanged(vaults[0].id)
      }

      console.log(`✅ Loaded ${vaults.length} vaults with ${contexts.length} contexts`)
    } catch (error) {
      console.error('❌ Failed to load vault structure:', error)
      throw error
    }
  }

  // Scan for vaults in the root directory
  private async scanVaults(vaultRootPath: string): Promise<UIVaultInfo[]> {
    const vaults: UIVaultInfo[] = []

    try {
      const pathExists = await window.electronAPI.vault.pathExists(vaultRootPath)
      if (!pathExists.exists) {
        return vaults
      }

      const dirResult = await window.electronAPI.vault.readDirectory(vaultRootPath)
      if (!dirResult.success) {
        return vaults
      }

      const items = dirResult.items

      for (const item of items) {
        if (item.name.startsWith('.')) continue // Skip system folders

        if (item.isDirectory) {
          const vaultMetadataPath = this.joinPath(item.path, '.vault', 'metadata.json')
          const metadataExists = await window.electronAPI.vault.pathExists(vaultMetadataPath)

          if (metadataExists.exists) {
            try {
              const metadataContent = await window.electronAPI.files.getFileContent(vaultMetadataPath)
              const metadata = JSON.parse(metadataContent.toString())

              // Count contexts
              const contextCount = await this.countContexts(item.path)

              // Calculate size (simplified)
              const size = await this.calculateFolderSize(item.path)

              vaults.push({
                id: metadata.id,
                name: metadata.name,
                path: item.path,
                contextCount,
                lastActivity: this.getRelativeTime(metadata.updatedAt),
                color: this.getVaultColor(metadata.color),
                icon: metadata.icon || 'folder',
                size: this.formatFileSize(size)
              })
            } catch (error) {
              console.warn('Failed to parse vault metadata:', vaultMetadataPath, error)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error scanning vaults:', error)
    }

    return vaults
  }

  // Scan for contexts across all vaults
  private async scanContexts(vaultRootPath: string, vaults: UIVaultInfo[]): Promise<UIContextInfo[]> {
    const contexts: UIContextInfo[] = []

    try {
      for (const vault of vaults) {
        const dirResult = await window.electronAPI.vault.readDirectory(vault.path)
        if (!dirResult.success) continue

        const items = dirResult.items

        for (const item of items) {
          if (item.name.startsWith('.')) continue // Skip system folders

          if (item.isDirectory) {
            const contextMetadataPath = this.joinPath(item.path, '.context', 'metadata.json')
            const masterDocPath = this.joinPath(item.path, 'master.md')

            const metadataExists = await window.electronAPI.vault.pathExists(contextMetadataPath)
            const masterExists = await window.electronAPI.vault.pathExists(masterDocPath)

            if (metadataExists.exists || masterExists.exists) {
              try {
                let contextData: any

                if (metadataExists.exists) {
                  const metadataContent = await window.electronAPI.files.getFileContent(contextMetadataPath)
                  contextData = JSON.parse(metadataContent.toString())
                } else {
                  // Create basic context data from folder
                  contextData = {
                    id: item.name,
                    name: item.name.charAt(0).toUpperCase() + item.name.slice(1).replace(/-/g, ' '),
                    vaultId: vault.id,
                    description: 'Context created from existing folder',
                    fileCount: 1,
                    lastActivity: new Date().toISOString()
                  }
                }

                contexts.push({
                  id: contextData.id,
                  name: contextData.name,
                  vaultId: vault.id,
                  fileCount: contextData.fileCount || 1,
                  chatCount: 0, // Will be loaded from database later
                  lastActivity: this.getRelativeTime(contextData.lastActivity || contextData.updatedAt),
                  description: contextData.description || ''
                })
              } catch (error) {
                console.warn('Failed to parse context metadata:', contextMetadataPath, error)
              }
            }
          }
        }
      }
    } catch (error) {
      console.error('Error scanning contexts:', error)
    }

    return contexts
  }

  // Count contexts in a vault
  private async countContexts(vaultPath: string): Promise<number> {
    try {
      const dirResult = await window.electronAPI.vault.readDirectory(vaultPath)
      if (!dirResult.success) return 0

      let count = 0

      for (const item of dirResult.items) {
        if (item.name.startsWith('.')) continue

        if (item.isDirectory) {
          const masterPath = this.joinPath(item.path, 'master.md')
          const contextPath = this.joinPath(item.path, '.context')

          const masterExists = await window.electronAPI.vault.pathExists(masterPath)
          const contextExists = await window.electronAPI.vault.pathExists(contextPath)

          if (masterExists.exists || contextExists.exists) {
            count++
          }
        }
      }

      return count
    } catch (error) {
      return 0
    }
  }

  // Calculate folder size (simplified)
  private async calculateFolderSize(folderPath: string): Promise<number> {
    try {
      const dirResult = await window.electronAPI.vault.readDirectory(folderPath)
      if (!dirResult.success) return 0

      let totalSize = 0

      for (const item of dirResult.items) {
        if (!item.isDirectory) {
          totalSize += item.size || 0
        } else if (!item.name.startsWith('.')) {
          totalSize += await this.calculateFolderSize(item.path)
        }
      }

      return totalSize
    } catch (error) {
      return 0
    }
  }

  // Helper method for path joining
  private joinPath(...parts: string[]): string {
    // Use the appropriate separator for the platform
    const separator = navigator.platform.toLowerCase().includes('win') ? '\\' : '/'
    return parts.join(separator).replace(/[\/\\]+/g, separator)
  }

  // Format file size
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  // Get relative time
  private getRelativeTime(dateString: string): string {
    try {
      const date = new Date(dateString)
      const now = new Date()
      const diffMs = now.getTime() - date.getTime()
      const diffMins = Math.floor(diffMs / 60000)
      const diffHours = Math.floor(diffMins / 60)
      const diffDays = Math.floor(diffHours / 24)

      if (diffMins < 1) return 'Just now'
      if (diffMins < 60) return `${diffMins} minutes ago`
      if (diffHours < 24) return `${diffHours} hours ago`
      if (diffDays < 7) return `${diffDays} days ago`
      if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
      return `${Math.floor(diffDays / 30)} months ago`
    } catch (error) {
      return 'Recently'
    }
  }

  // Get vault color class
  private getVaultColor(color: string): string {
    const colorMap: { [key: string]: string } = {
      '#8AB0BB': 'text-primary',
      '#FF8383': 'text-secondary',
      '#1B3E68': 'text-tertiary',
      '#D5D8E0': 'text-supplement1',
      '#89AFBA': 'text-supplement2'
    }
    return colorMap[color] || 'text-primary'
  }

  // Trigger UI refresh
  async refreshUI(vaultRootPath: string): Promise<void> {
    await this.resetUI()
    await new Promise(resolve => setTimeout(resolve, 100)) // Small delay for UI
    await this.loadVaultStructure(vaultRootPath)
  }
}
