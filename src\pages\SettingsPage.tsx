import React, { useState, useEffect } from 'react'
import { useAppStore } from '../store'
import { ArrowLeft, Key, Settings, User, FileText, Download, Folder, RefreshCw } from '../components/Icons'
import { useNavigate } from 'react-router-dom'
import { VaultManager } from '../services/vaultManager'
import { useVaultStore } from '../stores/vaultStore'

const SettingsPage: React.FC = () => {
  const navigate = useNavigate()
  const { settings, updateSettings } = useAppStore()
  const [activeTab, setActiveTab] = useState('api')
  const [localSettings, setLocalSettings] = useState(settings)
  const [isLoading, setIsLoading] = useState(false)
  const [testResult, setTestResult] = useState<string | null>(null)
  const [chatloPath, setChatloPath] = useState('')
  const [vaultRootPath, setVaultRootPath] = useState('')
  const [storageInfo, setStorageInfo] = useState<any>(null)
  // Use vault store for global state
  const {
    vaults,
    activeVaultId,
    vaultRootPath: storeVaultRootPath,
    setVaults,
    setActiveVaultId,
    setVaultRootPath: setStoreVaultRootPath,
    loadActiveVaultFromDB,
    saveVaultRootPathToDB
  } = useVaultStore()
  const [vaultManager] = useState(() => new VaultManager())
  const [isInitializing, setIsInitializing] = useState(false)
  const [initProgress, setInitProgress] = useState('')
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [pendingVaultPath, setPendingVaultPath] = useState('')
  const [pendingTemplate, setPendingTemplate] = useState<'default' | 'minimal' | 'complete' | null>(null)

  useEffect(() => {
    setLocalSettings(settings)
    loadChatloPath()
    loadVaultRootPath()
    loadStorageInfo()
    loadVaults()
    setupVaultManager()
  }, [settings])

  const setupVaultManager = () => {
    // Setup UI manager callbacks
    const uiManager = vaultManager.getUIManager()

    uiManager.onVaultsChange((newVaults) => {
      setVaults(newVaults)
    })

    uiManager.onActiveVaultChange((vaultId) => {
      setActiveVaultId(vaultId)
    })
  }

  const loadChatloPath = async () => {
    try {
      if (window.electronAPI?.files) {
        const path = await window.electronAPI.files.getChatloFolderPath()
        setChatloPath(path)
      }
    } catch (error) {
      console.error('Error loading Chatlo path:', error)
    }
  }

  const loadVaultRootPath = async () => {
    try {
      // First check if we have it in the store
      if (storeVaultRootPath) {
        console.log('📂 Using vault root path from store:', storeVaultRootPath)
        setVaultRootPath(storeVaultRootPath)
        return
      }

      // Otherwise load from settings
      if (window.electronAPI?.settings) {
        const savedVaultRoot = await window.electronAPI.settings.get('vaultRootPath')
        if (savedVaultRoot) {
          console.log('📂 Loaded vault root path from settings:', savedVaultRoot)
          setVaultRootPath(savedVaultRoot)
          setStoreVaultRootPath(savedVaultRoot)
        } else {
          // Set default vault root path if none exists
          const defaultVaultRoot = 'C:\\Users\\<USER>\\Documents\\ChatLo-Vaults'
          console.log('📂 Setting default vault root path:', defaultVaultRoot)
          setVaultRootPath(defaultVaultRoot)
          setStoreVaultRootPath(defaultVaultRoot)
          // Save the default to settings
          await saveVaultRootPathToDB(defaultVaultRoot)
        }
      }
    } catch (error) {
      console.error('Error loading vault root path:', error)
      // Fallback to default path
      const fallbackPath = 'C:\\Users\\<USER>\\Documents\\ChatLo-Vaults'
      setVaultRootPath(fallbackPath)
      setStoreVaultRootPath(fallbackPath)
    }
  }

  const loadVaults = async () => {
    try {
      // Load vaults using vault manager if vault root is set
      if (vaultRootPath && vaultManager) {
        await vaultManager.loadExistingStructure(vaultRootPath)
      } else {
        // Fallback to mock data for demo
        const mockVaults = [
          {
            id: 'personal-vault',
            name: 'Personal Vault',
            path: 'mock-vaults/personal-vault',
            contextCount: 2,
            lastActivity: '2 hours ago',
            color: 'text-primary',
            icon: 'user',
            size: '45.2 MB'
          },
          {
            id: 'work-vault',
            name: 'Work Vault',
            path: 'mock-vaults/work-vault',
            contextCount: 1,
            lastActivity: '1 hour ago',
            color: 'text-secondary',
            icon: 'briefcase',
            size: '28.7 MB'
          },
          {
            id: 'archive-vault',
            name: 'Archive Vault',
            path: 'mock-vaults/archive-vault',
            contextCount: 5,
            lastActivity: '2 weeks ago',
            color: 'text-supplement2',
            icon: 'archive',
            size: '156.3 MB'
          }
        ]
        setVaults(mockVaults)
      }

      // Load active vault from database
      await loadActiveVaultFromDB()
    } catch (error) {
      console.error('Error loading vaults:', error)
    }
  }

  const loadStorageInfo = async () => {
    try {
      if (window.electronAPI?.files) {
        const files = await window.electronAPI.files.getIndexedFiles()
        const totalSize = files.reduce((sum, file) => sum + file.file_size, 0)
        setStorageInfo({
          totalFiles: files.length,
          totalSize: totalSize,
          fileTypes: files.reduce((acc, file) => {
            acc[file.file_type] = (acc[file.file_type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        })
      }
    } catch (error) {
      console.error('Error loading storage info:', error)
    }
  }

  const handleSave = async () => {
    try {
      setIsLoading(true)
      updateSettings(localSettings)

      if (window.electronAPI?.settings) {
        await window.electronAPI.settings.set('app-settings', localSettings)
      }

      setTestResult('✅ Settings saved successfully!')
      setTimeout(() => setTestResult(null), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
      setTestResult('❌ Failed to save settings')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const testApiKey = async () => {
    if (!localSettings.openRouterApiKey) {
      setTestResult('❌ Please enter an API key first')
      return
    }

    try {
      setIsLoading(true)
      setTestResult('Testing API key...')

      // Import the service dynamically to avoid circular dependencies
      const { openRouterService } = await import('../services/openrouter')
      openRouterService.setApiKey(localSettings.openRouterApiKey)

      const validation = await openRouterService.validateApiKey()

      if (validation.valid) {
        setTestResult('✅ API key is valid!')
      } else {
        setTestResult('❌ Invalid API key')
      }
    } catch (error) {
      console.error('Error testing API key:', error)
      setTestResult('❌ Failed to test API key')
    } finally {
      setIsLoading(false)
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const handleSelectFolder = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Chatlo Folder',
          properties: ['openDirectory'],
          defaultPath: chatloPath
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          setTestResult('Updating folder location...')

          await window.electronAPI.files.setChatloFolderPath(newPath)
          setChatloPath(newPath)
          await loadStorageInfo() // Refresh storage info

          setTestResult('✅ Folder location updated successfully!')
          setTimeout(() => setTestResult(null), 3000)
        }
      }
    } catch (error) {
      console.error('Error selecting folder:', error)
      setTestResult('❌ Failed to update folder location')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSelectVaultRoot = async () => {
    try {
      setIsLoading(true)
      setIsInitializing(true)
      console.log('🔍 Current vault root path:', vaultRootPath)

      if (window.electronAPI?.files) {
        const result = await window.electronAPI.files.showOpenDialog({
          title: 'Select Vault Root Folder',
          properties: ['openDirectory'],
          defaultPath: vaultRootPath
        })

        if (!result.canceled && result.filePaths.length > 0) {
          const newPath = result.filePaths[0]
          console.log('📂 Selected new vault root path:', newPath)

          // Show confirmation toast
          setPendingVaultPath(newPath)
          setPendingTemplate(null)
          setShowConfirmDialog(true)
        } else {
          console.log('❌ Vault root selection canceled or no path selected')
        }
      }
    } catch (error) {
      console.error('Error selecting vault root:', error)
      setTestResult('❌ Failed to initialize vault structure')
      setInitProgress('')
    } finally {
      setIsLoading(false)
      setIsInitializing(false)
      setTimeout(() => {
        setTestResult(null)
        setInitProgress('')
      }, 5000)
    }
  }

  const handleCreateVault = async () => {
    const name = prompt('Enter vault name:')
    if (name && vaultRootPath) {
      try {
        setIsLoading(true)
        setTestResult('Creating vault...')

        // Create vault template
        const vaultTemplate = {
          id: name.toLowerCase().replace(/\s+/g, '-'),
          name,
          description: `${name} vault for organizing contexts`,
          color: '#8AB0BB',
          icon: 'folder',
          contexts: [
            {
              id: 'getting-started',
              name: 'Getting Started',
              description: `Welcome to ${name}! Start organizing your content here.`,
              tags: ['welcome', 'new']
            }
          ]
        }

        // Use vault manager to create vault
        await vaultManager.createVault(vaultTemplate)

        setTestResult('✅ Vault created successfully!')
        setTimeout(() => setTestResult(null), 3000)
      } catch (error) {
        console.error('Error creating vault:', error)
        setTestResult('❌ Failed to create vault')
        setTimeout(() => setTestResult(null), 3000)
      } finally {
        setIsLoading(false)
      }
    } else if (!vaultRootPath) {
      setTestResult('❌ Please set vault root path first')
      setTimeout(() => setTestResult(null), 3000)
    }
  }

  const handleSetActiveVault = async (vaultId: string) => {
    try {
      // Use vault store which automatically saves to DB
      setActiveVaultId(vaultId)

      setTestResult('✅ Active vault updated!')
      setTimeout(() => setTestResult(null), 2000)
    } catch (error) {
      console.error('Error setting active vault:', error)
      setTestResult('❌ Failed to set active vault')
      setTimeout(() => setTestResult(null), 3000)
    }
  }

  const handleInitializeWithTemplate = async (templateType: 'default' | 'minimal' | 'complete') => {
    if (!vaultRootPath) return

    // Show confirmation toast for template initialization
    setPendingVaultPath(vaultRootPath)
    setPendingTemplate(templateType)
    setShowConfirmDialog(true)
  }

  const handleConfirmInitialization = async () => {
    if (!pendingVaultPath) return

    // Check if vault API is available
    console.log('ElectronAPI available:', !!window.electronAPI)
    console.log('Vault API available:', !!window.electronAPI?.vault)
    console.log('Available APIs:', Object.keys(window.electronAPI || {}))

    if (!window.electronAPI?.vault) {
      setTestResult('❌ Vault API not available. Please restart the application.')
      setShowConfirmDialog(false)
      setTimeout(() => setTestResult(null), 5000)
      return
    }

    try {
      setIsLoading(true)
      setIsInitializing(true)
      setShowConfirmDialog(false)
      setTestResult('🚀 Initializing vault structure...')
      setInitProgress('Preparing templates...')

      let templates
      if (pendingTemplate) {
        switch (pendingTemplate) {
          case 'minimal':
            templates = VaultManager.getMinimalTemplates()
            break
          case 'complete':
            templates = VaultManager.getDefaultTemplates()
            break
          case 'default':
          default:
            templates = VaultManager.getDefaultTemplates().slice(0, 2) // Personal + Work only
            break
        }
      }

      const result = await vaultManager.changeVaultRoot(pendingVaultPath, {
        cleanExisting: true,
        useCustomTemplates: !!pendingTemplate,
        templates,
        showProgress: true
      })

      // Update the UI and store to reflect the new vault root path
      setVaultRootPath(pendingVaultPath)
      setStoreVaultRootPath(pendingVaultPath)
      console.log('✅ Vault root path updated in UI and store:', pendingVaultPath)

      if (result.success) {
        setVaultRootPath(pendingVaultPath)
        setTestResult(result.message)
      } else {
        setTestResult(result.message)
      }
    } catch (error) {
      console.error('Error initializing vault structure:', error)
      setTestResult('❌ Failed to initialize vault structure')
    } finally {
      setIsLoading(false)
      setIsInitializing(false)
      setInitProgress('')
      setPendingVaultPath('')
      setPendingTemplate(null)
      setTimeout(() => {
        setTestResult(null)
      }, 5000)
    }
  }

  const handleCancelInitialization = () => {
    setShowConfirmDialog(false)
    setPendingVaultPath('')
    setPendingTemplate(null)
  }

  const handleIndexFiles = async () => {
    try {
      setIsLoading(true)
      if (window.electronAPI?.files) {
        await window.electronAPI.files.indexAllFiles()
        await loadStorageInfo() // Refresh storage info
        setTestResult('✅ Files indexed successfully!')
        setTimeout(() => setTestResult(null), 3000)
      }
    } catch (error) {
      console.error('Error indexing files:', error)
      setTestResult('❌ Failed to index files')
      setTimeout(() => setTestResult(null), 3000)
    } finally {
      setIsLoading(false)
    }
  }

  const tabs = [
    { id: 'api', label: 'API Setup', icon: Key },
    { id: 'data', label: 'Data Management', icon: FileText },
    { id: 'profile', label: 'User Profile', icon: User },
  ]

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-supplement1">
      {/* Header */}
      <header className="flex items-center gap-4 h-16 px-6 border-b border-tertiary bg-gray-800/60 backdrop-blur-lg">
        <button
          onClick={() => navigate('/')}
          className="u1-button-ghost h-8 w-8 flex items-center justify-center"
        >
          <ArrowLeft className="h-4 w-4" />
        </button>
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h1 className="text-xl font-semibold text-supplement1">Settings</h1>
        </div>
      </header>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <nav className="w-64 border-r border-tertiary bg-gray-800/30">
          <div className="p-4">
            <div className="space-y-1">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`
                      w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm transition-colors
                      ${activeTab === tab.id
                        ? 'bg-primary text-gray-900'
                        : 'text-gray-400 hover:text-supplement1 hover:bg-gray-700'
                      }
                    `}
                  >
                    <Icon className="h-4 w-4" />
                    {tab.label}
                  </button>
                )
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 overflow-y-auto">
          <div className="max-w-4xl mx-auto p-8">
            {activeTab === 'api' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">API Configuration</h2>
                  <p className="text-gray-400">Configure your OpenRouter API key to access AI models.</p>
                </div>

                <div className="u1-card bg-gray-800/50 border border-gray-700">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium mb-2 text-supplement1">
                        OpenRouter API Key
                      </label>
                      <div className="flex gap-3">
                        <input
                          type="password"
                          value={localSettings.openRouterApiKey || ''}
                          onChange={(e) => setLocalSettings(prev => ({
                            ...prev,
                            openRouterApiKey: e.target.value
                          }))}
                          placeholder="sk-or-..."
                          className="u1-input-field flex-1"
                        />
                        <button
                          onClick={testApiKey}
                          disabled={isLoading}
                          className="u1-button-ghost px-4 py-2 disabled:opacity-50"
                        >
                          Test
                        </button>
                      </div>
                      {testResult && (
                        <p className="mt-2 text-sm">{testResult}</p>
                      )}
                    </div>

                    <div className="flex gap-3">
                      <button
                        onClick={handleSave}
                        disabled={isLoading}
                        className="u1-button-primary disabled:opacity-50"
                      >
                        Save Settings
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'data' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2 text-supplement1">Data Management</h2>
                  <p className="text-gray-400">Manage your files, conversations, and storage.</p>
                </div>

                {/* Confirmation Dialog */}
                {showConfirmDialog && (
                  <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
                    <div className="bg-gray-800 border border-gray-700 rounded-lg p-6 max-w-md mx-4 shadow-2xl">
                      <div className="flex items-center gap-3 mb-4">
                        <div className="w-8 h-8 bg-primary/20 rounded-full flex items-center justify-center">
                          <Folder className="h-4 w-4 text-primary" />
                        </div>
                        <h3 className="text-lg font-semibold text-supplement1">Initialize Vault Structure</h3>
                      </div>

                      <div className="mb-6">
                        <p className="text-gray-300 mb-2">
                          {pendingTemplate
                            ? `Initialize vault structure with ${pendingTemplate} template?`
                            : 'Initialize vault structure at:'
                          }
                        </p>
                        <div className="bg-gray-900/50 border border-gray-700 rounded p-3 mb-4">
                          <p className="text-sm font-mono text-gray-400 break-all">{pendingVaultPath}</p>
                        </div>
                        <p className="text-sm text-gray-400">
                          This will create the folder structure and empty contexts.
                          {pendingTemplate && ` Using the ${pendingTemplate} template with predefined vaults and contexts.`}
                        </p>
                      </div>

                      <div className="flex gap-3 justify-end">
                        <button
                          onClick={handleCancelInitialization}
                          disabled={isInitializing}
                          className="px-4 py-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg text-sm font-medium transition-colors disabled:opacity-50"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleConfirmInitialization}
                          disabled={isInitializing}
                          className="px-4 py-2 bg-primary hover:bg-primary/80 text-white rounded-lg text-sm font-medium transition-colors disabled:opacity-50 flex items-center gap-2"
                        >
                          {isInitializing ? (
                            <>
                              <RefreshCw className="h-4 w-4 animate-spin" />
                              Initializing...
                            </>
                          ) : (
                            <>
                              <Folder className="h-4 w-4" />
                              Initialize
                            </>
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Global Test Result */}
                {testResult && (
                  <div className={`p-4 rounded-lg border ${
                    testResult.includes('✅')
                      ? 'bg-green-900/20 border-green-700/50 text-green-300'
                      : testResult.includes('❌')
                      ? 'bg-red-900/20 border-red-700/50 text-red-300'
                      : 'bg-blue-900/20 border-blue-700/50 text-blue-300'
                  }`}>
                    <p className="text-sm font-medium">{testResult}</p>
                  </div>
                )}

                <div className="space-y-6">
                  {/* Vault Root Configuration */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                      <div className="w-5 h-5 bg-primary/20 rounded flex items-center justify-center">
                        <Folder className="h-3 w-3 text-primary" />
                      </div>
                      Vault Root Location
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-2 text-supplement1">
                          Vault Root Folder
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={vaultRootPath}
                            readOnly
                            className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
                            placeholder="No vault root selected"
                          />
                          <button
                            onClick={handleSelectVaultRoot}
                            disabled={isLoading}
                            className="px-4 py-2 bg-primary hover:bg-primary/90 text-gray-900 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
                          >
                            <Folder className="h-4 w-4" />
                            Browse
                          </button>
                        </div>
                        <p className="text-sm text-gray-400 mt-2">
                          Choose where to store all your context vaults. This can be a cloud-synced folder for backup.
                        </p>

                        {/* Debug: API Status */}
                        <div className="mt-4 p-3 bg-gray-900/50 border border-gray-700 rounded-lg">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="text-sm font-medium text-supplement1">API Status</p>
                              <p className="text-xs text-gray-400">
                                ElectronAPI: {window.electronAPI ? '✅ Available' : '❌ Not Available'} |
                                Vault API: {window.electronAPI?.vault ? '✅ Available' : '❌ Not Available'}
                              </p>
                              {window.electronAPI && (
                                <p className="text-xs text-gray-500 mt-1">
                                  Available APIs: {Object.keys(window.electronAPI).join(', ')}
                                </p>
                              )}
                            </div>
                            <button
                              onClick={() => window.location.reload()}
                              className="px-3 py-1 bg-primary/20 hover:bg-primary/30 text-primary rounded text-xs font-medium transition-colors"
                            >
                              Refresh
                            </button>
                          </div>
                        </div>

                        {/* Initialization Progress */}
                        {isInitializing && (
                          <div className="mt-4 p-3 bg-blue-900/20 border border-blue-700/50 rounded-lg">
                            <div className="flex items-center gap-2 mb-2">
                              <RefreshCw className="h-4 w-4 text-blue-400 animate-spin" />
                              <span className="text-sm font-medium text-blue-300">Initializing Vault Structure</span>
                            </div>
                            {initProgress && (
                              <p className="text-xs text-blue-400">{initProgress}</p>
                            )}
                          </div>
                        )}

                        {/* Template Selection */}
                        {vaultRootPath && !isInitializing && vaults.length === 0 && (
                          <div className="mt-4 p-4 bg-gray-800/30 border border-gray-700 rounded-lg">
                            <h4 className="text-sm font-medium text-supplement1 mb-3">Quick Setup Templates</h4>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                              <button
                                onClick={() => handleInitializeWithTemplate('default')}
                                disabled={isLoading}
                                className="p-3 bg-primary/10 border border-primary/30 rounded-lg hover:bg-primary/20 transition-colors text-left disabled:opacity-50"
                              >
                                <div className="text-sm font-medium text-primary">Default Setup</div>
                                <div className="text-xs text-gray-400 mt-1">Personal + Work vaults</div>
                              </button>
                              <button
                                onClick={() => handleInitializeWithTemplate('minimal')}
                                disabled={isLoading}
                                className="p-3 bg-secondary/10 border border-secondary/30 rounded-lg hover:bg-secondary/20 transition-colors text-left disabled:opacity-50"
                              >
                                <div className="text-sm font-medium text-secondary">Minimal Setup</div>
                                <div className="text-xs text-gray-400 mt-1">Single vault only</div>
                              </button>
                              <button
                                onClick={() => handleInitializeWithTemplate('complete')}
                                disabled={isLoading}
                                className="p-3 bg-supplement2/10 border border-supplement2/30 rounded-lg hover:bg-supplement2/20 transition-colors text-left disabled:opacity-50"
                              >
                                <div className="text-sm font-medium text-supplement2">Complete Setup</div>
                                <div className="text-xs text-gray-400 mt-1">Personal + Work + Learning</div>
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Vault Management */}
                  <div className="u1-card bg-gray-800/50 border border-gray-700">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium flex items-center gap-2">
                        <div className="w-5 h-5 bg-secondary/20 rounded flex items-center justify-center">
                          <Settings className="h-3 w-3 text-secondary" />
                        </div>
                        Context Vaults
                      </h3>
                      <button
                        onClick={handleCreateVault}
                        disabled={isLoading}
                        className="px-3 py-1 bg-secondary hover:bg-secondary/90 text-white rounded text-sm font-medium transition-colors disabled:opacity-50"
                      >
                        + New Vault
                      </button>
                    </div>

                    <div className="space-y-3">
                      {vaults.map((vault) => (
                        <div
                          key={vault.id}
                          className={`p-4 rounded-lg border transition-colors ${
                            activeVaultId === vault.id
                              ? 'border-primary bg-primary/10'
                              : 'border-gray-700 hover:border-gray-600'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className={`w-8 h-8 ${vault.color === 'text-primary' ? 'bg-primary/20' : vault.color === 'text-secondary' ? 'bg-secondary/20' : 'bg-supplement2/20'} rounded flex items-center justify-center`}>
                                <Folder className={`h-4 w-4 ${vault.color}`} />
                              </div>
                              <div>
                                <h4 className="font-medium text-supplement1">{vault.name}</h4>
                                <div className="flex items-center gap-4 text-sm text-gray-400">
                                  <span>{vault.contextCount} contexts</span>
                                  <span>{vault.size}</span>
                                  <span>{vault.lastActivity}</span>
                                </div>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              {activeVaultId === vault.id && (
                                <span className="text-xs bg-primary text-gray-900 px-2 py-1 rounded font-medium">
                                  Active
                                </span>
                              )}
                              {activeVaultId !== vault.id && (
                                <button
                                  onClick={() => handleSetActiveVault(vault.id)}
                                  className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-2 py-1 rounded transition-colors"
                                >
                                  Set Active
                                </button>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                      {vaults.length === 0 && (
                        <div className="text-center py-8 text-gray-400">
                          <Folder className="h-12 w-12 mx-auto mb-3 opacity-50" />
                          <p>No vaults found</p>
                          <p className="text-sm">Create your first vault to get started</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* File Storage */}
                    <div className="u1-card bg-gray-800/50 border border-gray-700">
                      <h3 className="text-lg font-medium mb-4">Legacy File Storage</h3>
                    <div className="space-y-4">
                      {/* Folder Selection */}
                      <div>
                        <label className="block text-sm font-medium mb-2">
                          Chatlo Folder Location
                        </label>
                        <div className="flex gap-2">
                          <input
                            type="text"
                            value={chatloPath}
                            readOnly
                            className="flex-1 bg-neutral-800 border border-neutral-700 rounded-lg px-3 py-2 text-sm font-mono text-neutral-300"
                          />
                          <button
                            onClick={handleSelectFolder}
                            className="px-3 py-2 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                            title="Select folder"
                          >
                            <Folder className="h-4 w-4" />
                            Browse
                          </button>
                          <button
                            onClick={handleIndexFiles}
                            disabled={isLoading}
                            className="px-3 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-sm font-medium transition-colors flex items-center gap-2 disabled:opacity-50"
                            title="Index files"
                          >
                            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                            Index Files
                          </button>
                        </div>
                      </div>

                      {/* Storage Stats */}
                      {storageInfo && (
                        <div className="pt-4 border-t border-neutral-700">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div className="flex justify-between">
                              <span className="text-neutral-400">Total Files:</span>
                              <span className="text-neutral-200">{storageInfo.totalFiles}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-neutral-400">Total Size:</span>
                              <span className="text-neutral-200">{formatFileSize(storageInfo.totalSize)}</span>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* File Types */}
                  {storageInfo && (
                    <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                      <h3 className="text-lg font-medium mb-4">File Types</h3>
                      <div className="space-y-2">
                        {Object.entries(storageInfo.fileTypes).map(([type, count]) => (
                          <div key={type} className="flex justify-between text-sm">
                            <span className="text-neutral-400 capitalize">{type}:</span>
                            <span className="text-neutral-200">{count}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'profile' && (
              <div className="space-y-6">
                <div>
                  <h2 className="text-2xl font-semibold mb-2">User Profile</h2>
                  <p className="text-neutral-400">Manage your profile and preferences.</p>
                </div>

                <div className="bg-neutral-900/50 border border-neutral-800 rounded-lg p-6">
                  <div className="text-center py-8">
                    <User className="h-12 w-12 text-neutral-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-neutral-400 mb-2">Coming Soon</h3>
                    <p className="text-sm text-neutral-500">
                      User profile features will be available in a future update.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </main>
      </div>
    </div>
  )
}

export default SettingsPage
