# Button Components

## Overview
Button components provide consistent interactive elements across the design system.

## Variants

### Primary Button
- Uses `colors.primary.500` background
- White text for contrast
- Hover state: `colors.primary.600`

### Secondary Button
- Uses `colors.secondary.500` background
- White text for contrast
- Hover state: `colors.secondary.600`

### Ghost Button
- Transparent background
- `colors.primary.500` text and border
- Hover state: `colors.primary.50` background

## Sizes
- Small: `spacing.sm` padding, `typography.fontSize.sm`
- Medium: `spacing.md` padding, `typography.fontSize.base`
- Large: `spacing.lg` padding, `typography.fontSize.lg`

## Accessibility
- Minimum 44px touch target
- WCAG AA color contrast compliance
- Focus indicators with 2px outline
- Screen reader compatible labels

## Usage Examples

```tsx
<Button variant="primary" size="medium">
  Save Changes
</Button>

<Button variant="secondary" size="small">
  Cancel
</Button>
```
