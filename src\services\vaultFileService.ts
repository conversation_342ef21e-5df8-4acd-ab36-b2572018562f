// Vault File Service - Routes file operations to vault structure
import { FileRecord } from '../types'

export interface VaultFileRecord extends FileRecord {
  contextId?: string
  vaultId?: string
  contextPath?: string
}

export class VaultFileService {
  private vaultRootPath: string = ''

  constructor() {
    this.loadVaultRootPath()
  }

  private async loadVaultRootPath(): Promise<void> {
    try {
      if (window.electronAPI?.settings) {
        const savedVaultRoot = await window.electronAPI.settings.get('vaultRootPath')
        if (savedVaultRoot) {
          this.vaultRootPath = savedVaultRoot
        }
      }
    } catch (error) {
      console.error('Error loading vault root path:', error)
    }
  }

  // Get the "No Context Vault" folder path
  private getNoContextVaultPath(): string {
    return this.joinPath(this.vaultRootPath, 'no-context-vault')
  }

  // Get context vault path
  private getContextVaultPath(contextId: string): string {
    // This would need to be enhanced to get the actual context path from the vault store
    return this.joinPath(this.vaultRootPath, 'personal-vault', contextId)
  }

  // Helper to join paths (cross-platform)
  private joinPath(...parts: string[]): string {
    return parts.join('/')
  }

  // Search files within a specific context
  async searchFilesInContext(query: string, contextId: string | null, limit: number = 10): Promise<VaultFileRecord[]> {
    try {
      if (!window.electronAPI?.files) {
        return []
      }

      // If no context selected, search in "no-context-vault"
      if (!contextId) {
        return await this.searchFilesInNoContextVault(query, limit)
      }

      // Search in specific context vault
      return await this.searchFilesInSpecificContext(query, contextId, limit)
    } catch (error) {
      console.error('Error searching files in context:', error)
      return []
    }
  }

  // Search files in the "no-context-vault" folder
  private async searchFilesInNoContextVault(query: string, limit: number): Promise<VaultFileRecord[]> {
    const noContextPath = this.getNoContextVaultPath()
    
    // Ensure the no-context-vault folder exists
    await this.ensureNoContextVaultExists()
    
    // Get all files from legacy system for now (will be migrated)
    const allFiles = await window.electronAPI!.files.searchFiles(query, limit)
    
    // Mark them as no-context files
    return allFiles.map(file => ({
      ...file,
      contextId: null,
      vaultId: null,
      contextPath: noContextPath
    }))
  }

  // Search files in a specific context vault
  private async searchFilesInSpecificContext(query: string, contextId: string, limit: number): Promise<VaultFileRecord[]> {
    // For now, return empty array - this will be implemented when vault file indexing is ready
    console.log('Searching in context:', contextId, 'query:', query)
    return []
  }

  // Ensure the "no-context-vault" folder exists
  private async ensureNoContextVaultExists(): Promise<void> {
    try {
      if (!window.electronAPI?.vault) {
        return
      }

      const noContextPath = this.getNoContextVaultPath()
      const pathExists = await window.electronAPI.vault.pathExists(noContextPath)
      
      if (!pathExists.exists) {
        // Create the no-context-vault folder structure
        await window.electronAPI.vault.createDirectory(noContextPath)
        await window.electronAPI.vault.createDirectory(this.joinPath(noContextPath, 'documents'))
        await window.electronAPI.vault.createDirectory(this.joinPath(noContextPath, 'images'))
        await window.electronAPI.vault.createDirectory(this.joinPath(noContextPath, 'uploads'))
        
        // Create a README file
        const readmeContent = `# No Context Vault

This folder contains files that are not associated with any specific context vault.

## Usage
- Files uploaded or pasted in chat without a context selection will be stored here
- Use the context vault selector in chat to organize files into specific contexts
- Files here are available across all conversations

## Folder Structure
- **documents/**: Text documents, PDFs, Word files
- **images/**: Images for AI vision analysis  
- **uploads/**: Files uploaded through the chat interface
`
        await window.electronAPI.vault.writeFile(
          this.joinPath(noContextPath, 'README.md'), 
          readmeContent
        )
        
        console.log('✅ Created no-context-vault folder')
      }
    } catch (error) {
      console.error('Error ensuring no-context-vault exists:', error)
    }
  }

  // Copy file to appropriate vault location
  async copyFileToVault(sourcePath: string, contextId: string | null, filename?: string): Promise<string> {
    try {
      if (!window.electronAPI?.vault) {
        throw new Error('Vault API not available')
      }

      const targetFilename = filename || this.getFilenameFromPath(sourcePath)
      let targetPath: string

      if (!contextId) {
        // Copy to no-context-vault
        await this.ensureNoContextVaultExists()
        const subfolder = this.getSubfolderForFile(targetFilename)
        targetPath = this.joinPath(this.getNoContextVaultPath(), subfolder, targetFilename)
      } else {
        // Copy to specific context vault
        const contextPath = this.getContextVaultPath(contextId)
        const subfolder = this.getSubfolderForFile(targetFilename)
        targetPath = this.joinPath(contextPath, subfolder, targetFilename)
      }

      // Ensure unique filename
      targetPath = await this.ensureUniqueFilename(targetPath)

      // Copy the file (for now, use legacy system)
      // TODO: Implement direct vault file copying
      const copiedPath = await window.electronAPI.files.copyFileToUploads(sourcePath, targetFilename)
      
      return copiedPath
    } catch (error) {
      console.error('Error copying file to vault:', error)
      throw error
    }
  }

  // Get appropriate subfolder for file type
  private getSubfolderForFile(filename: string): string {
    const ext = filename.toLowerCase().split('.').pop() || ''
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
      return 'images'
    } else if (['pdf', 'doc', 'docx', 'txt', 'md'].includes(ext)) {
      return 'documents'
    } else {
      return 'uploads'
    }
  }

  // Extract filename from path
  private getFilenameFromPath(filePath: string): string {
    return filePath.split(/[/\\]/).pop() || 'unknown'
  }

  // Ensure unique filename by adding counter if needed
  private async ensureUniqueFilename(targetPath: string): Promise<string> {
    if (!window.electronAPI?.vault) {
      return targetPath
    }

    let finalPath = targetPath
    let counter = 1
    
    while (true) {
      const pathExists = await window.electronAPI.vault.pathExists(finalPath)
      if (!pathExists.exists) {
        break
      }
      
      const ext = finalPath.split('.').pop()
      const nameWithoutExt = finalPath.substring(0, finalPath.lastIndexOf('.'))
      finalPath = `${nameWithoutExt}_${counter}.${ext}`
      counter++
    }
    
    return finalPath
  }

  // Get all files for a specific context
  async getFilesForContext(contextId: string | null): Promise<VaultFileRecord[]> {
    return await this.searchFilesInContext('', contextId, 50)
  }

  // Migrate legacy files to vault structure
  async migrateLegacyFiles(): Promise<void> {
    console.log('🔄 Starting legacy file migration to vault structure...')
    // TODO: Implement migration logic
    // 1. Get all files from legacy system
    // 2. Move them to no-context-vault
    // 3. Update database references
    console.log('⚠️ Legacy file migration not yet implemented')
  }
}

// Export singleton instance
export const vaultFileService = new VaultFileService()
