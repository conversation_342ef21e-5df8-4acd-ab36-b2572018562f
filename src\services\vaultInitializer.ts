// Vault Initializer Service
// Creates file structure and initializes empty contexts when vault root changes

// Use Electron API for file system operations

export interface VaultTemplate {
  id: string
  name: string
  description: string
  color: string
  icon: string
  contexts: ContextTemplate[]
}

export interface ContextTemplate {
  id: string
  name: string
  description: string
  tags: string[]
  color?: string
  icon?: string
}

export class VaultInitializer {
  private vaultRootPath: string

  constructor(vaultRootPath: string) {
    this.vaultRootPath = vaultRootPath
  }

  // Main initialization function
  async initializeVaultStructure(templates?: VaultTemplate[]): Promise<void> {
    console.log('🚀 Initializing vault structure at:', this.vaultRootPath)

    // Check if vault API is available
    if (!window.electronAPI?.vault) {
      console.error('Vault API not available. Please restart the application.')
      throw new Error('Vault API not available. Please restart the application to use vault features.')
    }

    try {
      // 1. Create vault root directory
      await this.createVaultRoot()

      // 2. Create system metadata
      await this.createSystemMetadata()

      // 3. Create default vaults or use templates
      const vaultTemplates = templates || this.getDefaultVaultTemplates()
      
      for (const vaultTemplate of vaultTemplates) {
        await this.createVault(vaultTemplate)
      }

      console.log('✅ Vault structure initialized successfully')
    } catch (error) {
      console.error('❌ Failed to initialize vault structure:', error)
      throw error
    }
  }

  // Create vault root directory and system files
  private async createVaultRoot(): Promise<void> {
    await window.electronAPI.vault.createDirectory(this.vaultRootPath)

    // Create .chatlo system directory
    const systemDir = this.joinPath(this.vaultRootPath, '.chatlo')
    await window.electronAPI.vault.createDirectory(systemDir)
  }

  // Create system metadata files
  private async createSystemMetadata(): Promise<void> {
    const systemDir = this.joinPath(this.vaultRootPath, '.chatlo')

    // Vault registry
    const registryPath = this.joinPath(systemDir, 'vault-registry.json')
    const registry = {
      version: '1.0.0',
      createdAt: new Date().toISOString(),
      vaults: [],
      activeVaultId: null
    }
    await window.electronAPI.vault.writeFile(registryPath, JSON.stringify(registry, null, 2))

    // Global preferences
    const preferencesPath = this.joinPath(systemDir, 'preferences.json')
    const preferences = {
      vaultRootPath: this.vaultRootPath,
      searchSettings: {
        includeContent: true,
        maxResults: 50
      },
      uiSettings: {
        defaultView: 'master',
        showArtifacts: true
      }
    }
    await window.electronAPI.vault.writeFile(preferencesPath, JSON.stringify(preferences, null, 2))
  }

  // Create individual vault
  private async createVault(template: VaultTemplate): Promise<void> {
    const vaultPath = this.joinPath(this.vaultRootPath, template.id)

    // Create vault directory
    await window.electronAPI.vault.createDirectory(vaultPath)

    // Create vault metadata directory
    const vaultMetaDir = this.joinPath(vaultPath, '.vault')
    await window.electronAPI.vault.createDirectory(vaultMetaDir)

    // Create vault metadata file
    const vaultMetadata = {
      id: template.id,
      name: template.name,
      path: vaultPath,
      description: template.description,
      color: template.color,
      icon: template.icon,
      isActive: template.id === 'personal-vault', // Set first vault as active
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    const metadataPath = this.joinPath(vaultMetaDir, 'metadata.json')
    await window.electronAPI.vault.writeFile(metadataPath, JSON.stringify(vaultMetadata, null, 2))

    // Create contexts in this vault
    for (const contextTemplate of template.contexts) {
      await this.createContext(vaultPath, template.id, contextTemplate)
    }

    console.log(`✅ Created vault: ${template.name}`)
  }

  // Create individual context
  private async createContext(vaultPath: string, vaultId: string, template: ContextTemplate): Promise<void> {
    const contextPath = this.joinPath(vaultPath, template.id)

    // Create context directory structure
    await window.electronAPI.vault.createDirectory(contextPath)
    await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, '.context'))
    await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, '.context', 'insights'))
    await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, 'documents'))
    await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, 'images'))
    await window.electronAPI.vault.createDirectory(this.joinPath(contextPath, 'artifacts'))

    // Create master.md (central intelligence hub)
    const masterContent = this.generateMasterTemplate(template)
    await window.electronAPI.vault.writeFile(this.joinPath(contextPath, 'master.md'), masterContent)

    // Create context metadata
    const contextMetadata = {
      id: template.id,
      vaultId: vaultId,
      name: template.name,
      folderPath: contextPath,
      masterDocPath: this.joinPath(contextPath, 'master.md'),
      description: template.description,
      tags: template.tags,
      color: template.color || '#8AB0BB',
      icon: template.icon || 'folder',
      isPinned: false,
      fileCount: 1, // master.md
      lastActivity: new Date().toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    const contextMetadataPath = this.joinPath(contextPath, '.context', 'metadata.json')
    await window.electronAPI.vault.writeFile(contextMetadataPath, JSON.stringify(contextMetadata, null, 2))

    // Create AI memory file
    const aiMemory = {
      contextId: template.id,
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString(),
      insights: [],
      patterns: [],
      relationships: [],
      conversationSummaries: []
    }
    await window.electronAPI.vault.writeFile(this.joinPath(contextPath, '.context', 'ai-memory.json'), JSON.stringify(aiMemory, null, 2))

    // Create insight files
    const insightsPath = this.joinPath(contextPath, '.context', 'insights')
    await window.electronAPI.vault.writeFile(this.joinPath(insightsPath, 'relationships.md'), '# File Relationships\n\n*AI will discover and document file relationships here*\n')
    await window.electronAPI.vault.writeFile(this.joinPath(insightsPath, 'patterns.md'), '# Discovered Patterns\n\n*AI will identify and document patterns here*\n')
    await window.electronAPI.vault.writeFile(this.joinPath(insightsPath, 'evolution.md'), '# Context Evolution Log\n\n*AI will track context changes and growth here*\n')

    console.log(`  ✅ Created context: ${template.name}`)
  }

  // Generate master.md template
  private generateMasterTemplate(template: ContextTemplate): string {
    return `# ${template.name}

Welcome to your **${template.name}** context! This is your intelligent workspace where AI learns from your files and conversations to provide contextual assistance.

## 🚀 Getting Started

### Step 1: Add Your Files
- **Drag & drop** files into the \`documents/\` folder
- **Supported formats**: PDF, Word, Excel, PowerPoint, Markdown, Text, Images
- **AI will automatically** analyze and understand your content

### Step 2: Start Conversations
- **Chat with AI** about your files and context
- **Ask questions** like "Summarize the key points" or "Find connections between documents"
- **AI remembers** everything and builds understanding over time

### Step 3: Watch AI Learn
- **AI updates this master.md** with insights and patterns
- **Relationships emerge** between files and concepts
- **Context grows smarter** with each interaction

## 🧠 AI Context Intelligence

### Current Understanding
*AI will update this section as it learns*

**Status**: 🆕 New context, ready to learn
**Content**: No files added yet
**Insights**: Waiting for first documents
**Last AI Update**: ${new Date().toISOString()}

### Discovered Patterns
*AI will identify patterns in your content*

- 📄 **Document Types**: Will be categorized automatically
- 🔗 **Relationships**: Connections between files will be mapped
- 📊 **Topics**: Key themes will be extracted
- 💡 **Insights**: AI will generate actionable insights

## 📋 Context Overview

**Purpose**: ${template.description}
**Tags**: ${template.tags.join(', ')}
**Created**: ${new Date().toLocaleDateString()}

### What This Context Is For
- **Organize** related documents and conversations
- **Build knowledge** through AI analysis
- **Discover insights** across your content
- **Maintain context** for better AI assistance

### How to Use This Context
1. **Add files** to the documents folder
2. **Chat about your content** with AI
3. **Review AI insights** in this master.md
4. **Build on discoveries** with more content

## 🎯 AI Focus Areas

**Current Priorities**:
${template.tags.map(tag => `- **${tag}**: AI will pay special attention to ${tag}-related content`).join('\n')}

**AI Will Watch For**:
- 📄 New documents and their content
- 💬 Conversations about this context
- 🔗 Connections to other contexts
- 📈 Emerging patterns and trends

## 🤖 AI Assistant Instructions

**Personality**: Helpful, learning-oriented, contextually aware
**Communication Style**: Clear, insightful, adaptive to your needs
**Focus**: Understanding your content and providing relevant assistance

**When You Chat**:
- AI will reference files in this context
- AI will build on previous conversations
- AI will suggest connections and insights
- AI will help you discover new perspectives

## 📊 Context Statistics

**Content Summary**:
- 📄 **Documents**: 0 files (add some to get started!)
- 💬 **Conversations**: 0 chats linked to this context
- 🔗 **Connections**: 0 relationships discovered
- 📈 **Activity**: Just created, ready for content

**Health Status**: ✅ Ready for content
**Growth Stage**: 🌱 New context, tracking begins now

## 🔗 Quick Actions

### Add Content
- Drop files in \`documents/\` folder
- Paste images in \`images/\` folder
- Save AI-generated content in \`artifacts/\`

### Start Learning
- Ask AI: "What should I add to this context?"
- Try: "Analyze my documents for key themes"
- Explore: "How do these files relate to each other?"

### Build Knowledge
- Review AI insights regularly
- Ask follow-up questions
- Connect ideas across contexts

---

## 🎉 Welcome to Intelligent Context Management!

This master.md file is your **context control center**. As you add files and chat with AI, this document will evolve to reflect your growing knowledge base.

**Next Steps**:
1. 📁 Add your first document to the \`documents/\` folder
2. 💬 Start a conversation about your content
3. 🧠 Watch AI build understanding in real-time

*This master.md is continuously updated by AI to reflect evolving understanding*
*Created: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}*
*Last Updated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}*`
  }

  // Get default vault templates
  private getDefaultVaultTemplates(): VaultTemplate[] {
    return [
      {
        id: 'personal-vault',
        name: 'Personal Vault',
        description: 'Personal projects and research',
        color: '#8AB0BB',
        icon: 'user',
        contexts: [
          {
            id: 'getting-started',
            name: 'Getting Started',
            description: 'Welcome to your first context! This is where you can start organizing your thoughts and files.',
            tags: ['welcome', 'tutorial', 'getting-started']
          }
        ]
      },
      {
        id: 'work-vault',
        name: 'Work Vault',
        description: 'Professional projects and work-related contexts',
        color: '#FF8383',
        icon: 'briefcase',
        contexts: [
          {
            id: 'projects',
            name: 'Projects',
            description: 'Work projects and professional development',
            tags: ['work', 'projects', 'professional']
          }
        ]
      }
    ]
  }

  // Clean existing structure (for reset)
  async cleanVaultStructure(): Promise<void> {
    // Check if vault API is available
    if (!window.electronAPI?.vault) {
      console.error('Vault API not available. Please restart the application.')
      throw new Error('Vault API not available. Please restart the application to use vault features.')
    }

    const pathExists = await window.electronAPI.vault.pathExists(this.vaultRootPath)
    if (pathExists.exists) {
      console.log('🧹 Cleaning existing vault structure...')
      await window.electronAPI.vault.removeDirectory(this.vaultRootPath)
      console.log('✅ Vault structure cleaned')
    }
  }

  // Helper method for path joining (since we can't use path module directly)
  private joinPath(...parts: string[]): string {
    // Use the appropriate separator for the platform
    const separator = navigator.platform.toLowerCase().includes('win') ? '\\' : '/'
    return parts.join(separator).replace(/[\/\\]+/g, separator)
  }

  // Update vault root path
  setVaultRootPath(newPath: string): void {
    this.vaultRootPath = newPath
  }
}
