# Client Project - E-commerce Platform

## 🧠 AI Context Summary
*This section is continuously updated by AI*

**Current Understanding**: E-commerce platform redesign with focus on conversion optimization
**Key Relationships**: User research → Design system → Implementation → Testing
**Active Patterns**: Conversion-focused design, mobile-first approach, performance optimization
**Last AI Update**: 2025-01-21T16:00:00Z

## 📋 Human Overview
*Human-maintained section*

**Purpose**: Redesign client's e-commerce platform to improve conversion rates and user experience
**Goals**: 
- Increase conversion rate by 25%
- Improve mobile experience (60% of traffic)
- Reduce page load times by 40%
- Implement accessibility standards

**Status**: Design phase 80% complete, development starting next week
**Notes**: Client feedback incorporated, ready for development handoff

## 🔗 Intelligent Connections

### File Relationships
*AI-discovered and human-curated*
- `user-journey-map.pdf` → `wireframes/checkout-flow.fig` (Journey to design)
- `brand-guidelines.pdf` → `design-system/components.fig` (Brand to implementation)
- `performance-audit.md` → `technical-requirements.md` (Analysis to specs)

### Concept Map
*AI-generated concept relationships*
- User Experience → Conversion Optimization → Business Goals
- Performance → User Satisfaction → Retention
- Accessibility → Compliance → Market Reach

### Cross-Context Links
*Links to related contexts in other vaults*
- Uses: `personal-vault/project-alpha` (shared design system components)
- References: `personal-vault/research-notes` (e-commerce UX research)

## 📚 Knowledge Evolution

### Recent Insights
*AI-generated insights from recent interactions*
- 2025-01-21: Identified checkout abandonment pattern in mobile flow
- 2025-01-20: Discovered performance bottleneck in product image loading
- 2025-01-19: Found accessibility improvements needed in search functionality

### Learning Trajectory
*AI tracks how understanding has evolved*
- Week 1: Client requirements gathering
- Week 2: User research and analysis
- Week 3: Design system adaptation
- Week 4: Prototype development and testing

## 🎯 Active Focus Areas
*What AI should pay attention to*

**Current Priorities**:
1. Mobile checkout flow optimization
2. Performance improvement implementation
3. Accessibility compliance verification

**Watch For**:
- Client feedback on prototypes
- Performance metrics during development
- User testing results

## 🤖 AI Instructions
*How AI should behave in this context*

**Personality**: Project consultant, results-oriented, client-focused
**Communication Style**: Professional, metrics-driven, solution-focused
**Focus Areas**: Conversion optimization, performance, user experience
**Avoid**: Generic recommendations, non-measurable suggestions

**Context Loading Priority**:
1. Always read this master.md first
2. Check client requirements and constraints
3. Load relevant design and research files
4. Reference performance and conversion metrics

## 📊 Context Metrics
*AI-maintained statistics*

**Files**: 28 documents, 15 images, 22 artifacts
**Conversations**: 31 chats linked to this context
**Activity**: Last updated 1 hour ago
**Health**: All files indexed, 1 client approval pending
**Growth**: +5 files this week, +8 conversations

---
*This master.md is continuously updated by AI to reflect evolving understanding*
*Created: 2025-01-08*
*Last Updated: 2025-01-21*
