# File Relationships

*AI-discovered and documented file relationships*

## Primary Dependencies

### Design Tokens → Components
- `design-tokens.json` → `components/buttons.md` (Color and spacing tokens)
- `design-tokens.json` → `components/forms.md` (Input styling tokens)
- `design-tokens.json` → `components/navigation.md` (Navigation theming)

### Documentation Hierarchy
- `README.md` → `docs/getting-started.md` (Entry point documentation)
- `style-guide.pdf` → All component files (Visual specification source)

## Secondary Relationships

### Cross-Reference Patterns
- Component files reference each other for consistency
- All components link back to accessibility guidelines
- Examples in docs reference actual component implementations

## Relationship Strength

**Strong Dependencies (0.8-1.0)**:
- Design tokens to component styling
- Style guide to visual implementations

**Medium Dependencies (0.5-0.8)**:
- Component cross-references
- Documentation links

**Weak Dependencies (0.2-0.5)**:
- Example code references
- Related reading suggestions

---
*Last updated by AI: 2025-01-21T14:30:00Z*
